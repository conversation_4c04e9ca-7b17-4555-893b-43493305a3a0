import React from 'react';
import { 
  Typography, Box, Paper, Table, TableBody, 
  TableCell, TableContainer, TableHead, TableRow,
  Button, Chip
} from '@mui/material';
import { Add } from '@mui/icons-material';

// Sample data
const brands = [
  { id: 1, name: 'Brand A', skus: 12, active: true },
  { id: 2, name: 'Brand B', skus: 8, active: true },
  { id: 3, name: 'Brand C', skus: 5, active: false },
  { id: 4, name: 'Brand D', skus: 15, active: true },
];

export default function BrandList() {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Brands
        </Typography>
        <Button variant="contained" startIcon={<Add />}>
          Add Brand
        </Button>
      </Box>
      
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Name</TableCell>
              <TableCell align="right">SKUs</TableCell>
              <TableCell align="right">Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {brands.map((brand) => (
              <TableRow key={brand.id}>
                <TableCell>{brand.id}</TableCell>
                <TableCell>{brand.name}</TableCell>
                <TableCell align="right">{brand.skus}</TableCell>
                <TableCell align="right">
                  <Chip
                    label={brand.active ? 'Active' : 'Inactive'}
                    color={brand.active ? 'success' : 'error'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Button size="small" variant="outlined">View</Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}




