import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Alert,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import apiService from '../../services/apiService';

export default function DSRManagement() {
  const [dsrs, setDSRs] = useState([]);
  const [distributors, setDistributors] = useState([]);
  const [territories, setTerritories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingDSR, setEditingDSR] = useState(null);
  const [message, setMessage] = useState({ type: '', text: '' });

  const [formData, setFormData] = useState({
    name: '',
    employee_code: '',
    phone_number: '',
    email: '',
    distributor: '',
    territory: '',
    is_active: true
  });

  useEffect(() => {
    loadDSRs();
    loadDistributors();
    loadTerritories();
  }, []);

  const loadDSRs = async () => {
    try {
      setLoading(true);
      const response = await apiService.getDSRs();
      setDSRs(response.results || response);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to load DSRs' });
    } finally {
      setLoading(false);
    }
  };

  const loadDistributors = async () => {
    try {
      const response = await apiService.getDistributors();
      setDistributors(response.results || response);
    } catch (error) {
      console.error('Failed to load distributors:', error);
    }
  };

  const loadTerritories = async () => {
    try {
      const response = await apiService.getTerritories();
      setTerritories(response.results || response);
    } catch (error) {
      console.error('Failed to load territories:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      if (editingDSR) {
        await apiService.updateDSR(editingDSR.id, formData);
        setMessage({ type: 'success', text: 'DSR updated successfully!' });
      } else {
        await apiService.createDSR(formData);
        setMessage({ type: 'success', text: 'DSR created successfully!' });
      }
      
      setDialogOpen(false);
      resetForm();
      loadDSRs();
    } catch (error) {
      setMessage({ type: 'error', text: error.message || 'Operation failed' });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (dsr) => {
    setEditingDSR(dsr);
    setFormData({
      name: dsr.name || '',
      employee_code: dsr.employee_code || '',
      phone_number: dsr.phone_number || '',
      email: dsr.email || '',
      distributor: dsr.distributor || '',
      territory: dsr.territory || '',
      is_active: dsr.is_active !== undefined ? dsr.is_active : true
    });
    setDialogOpen(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this DSR?')) {
      try {
        setLoading(true);
        await apiService.deleteDSR(id);
        setMessage({ type: 'success', text: 'DSR deleted successfully!' });
        loadDSRs();
      } catch (error) {
        setMessage({ type: 'error', text: error.message || 'Delete failed' });
      } finally {
        setLoading(false);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      employee_code: '',
      phone_number: '',
      email: '',
      distributor: '',
      territory: '',
      is_active: true
    });
    setEditingDSR(null);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    resetForm();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" fontWeight={600} mb={1}>
            DSR Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage District Sales Representatives
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setDialogOpen(true)}
        >
          Add DSR
        </Button>
      </Box>

      {message.text && (
        <Alert 
          severity={message.type} 
          sx={{ mb: 3 }}
          onClose={() => setMessage({ type: '', text: '' })}
        >
          {message.text}
        </Alert>
      )}

      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>DSR</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Contact</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Distributor</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Territory</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {dsrs.map((dsr) => (
                  <TableRow key={dsr.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PersonIcon sx={{ mr: 2, color: 'primary.main' }} />
                        <Box>
                          <Typography variant="body2" fontWeight={600}>
                            {dsr.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {dsr.employee_code}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        {dsr.phone_number && (
                          <Typography variant="body2">{dsr.phone_number}</Typography>
                        )}
                        {dsr.email && (
                          <Typography variant="caption" color="text.secondary">
                            {dsr.email}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {dsr.distributor_name || 'Not Assigned'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {dsr.territory_name ? (
                        <Chip 
                          label={dsr.territory_name} 
                          size="small" 
                          variant="outlined"
                        />
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Not Assigned
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={dsr.is_active ? 'Active' : 'Inactive'}
                        color={dsr.is_active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(dsr)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(dsr.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingDSR ? 'Edit DSR' : 'Add New DSR'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="name"
                label="Name"
                fullWidth
                required
                value={formData.name}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="employee_code"
                label="Employee Code"
                fullWidth
                required
                value={formData.employee_code}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="phone_number"
                label="Phone Number"
                fullWidth
                value={formData.phone_number}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="email"
                label="Email"
                type="email"
                fullWidth
                value={formData.email}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Distributor</InputLabel>
                <Select
                  name="distributor"
                  value={formData.distributor}
                  onChange={handleInputChange}
                  label="Distributor"
                >
                  {distributors.map((distributor) => (
                    <MenuItem key={distributor.id} value={distributor.id}>
                      {distributor.distributor_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Territory</InputLabel>
                <Select
                  name="territory"
                  value={formData.territory}
                  onChange={handleInputChange}
                  label="Territory"
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {territories.map((territory) => (
                    <MenuItem key={territory.id} value={territory.id}>
                      {territory.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Saving...' : (editingDSR ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
