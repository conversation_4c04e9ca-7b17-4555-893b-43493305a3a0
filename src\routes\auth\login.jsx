import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box, Paper, Typography, TextField, Button
} from '@mui/material';

export default function Login() {
  const [phone, setPhone] = useState('');
  const navigate = useNavigate();

  const handleLogin = (e) => {
    e.preventDefault();

    if (phone.trim() !== '') {
      // Simulate login with phone number and redirect
      console.log('Phone entered:', phone);
      navigate('/verify-otp'); // ✅ This must match the route in AppRoutes
    } else {
      alert('Please enter your phone number.');
    }
  };

  return (
    <Box sx={{ minHeight: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center', bgcolor: '#f5f5f5' }}>
      <Paper sx={{ p: 4, width: 360 }}>
        <Typography variant="h5" mb={2} align="center">Login</Typography>
        <form onSubmit={handleLogin}>
          <TextField
            fullWidth
            label="Phone Number"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            margin="normal"
            required
          />
          <Button fullWidth variant="contained" type="submit" sx={{ mt: 2 }}>
            Send OTP
          </Button>
        </form>
      </Paper>
    </Box>
  );
}
