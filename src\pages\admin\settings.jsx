import React from 'react';
import { 
  Typo<PERSON>, Box, Paper, Grid, 
  TextField, Button, Divider, 
  FormControlLabel, Switch
} from '@mui/material';

export default function AdminSettings() {
  const handleSave = () => {
    console.log('Settings saved');
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        System Settings
      </Typography>
      
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          General Settings
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Company Name"
              defaultValue="Innov8 Corporation"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Support Email"
              defaultValue="<EMAIL>"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="System Announcement"
              multiline
              rows={3}
              placeholder="Enter system-wide announcement here"
            />
          </Grid>
        </Grid>
        
        <Divider sx={{ my: 3 }} />
        
        <Typography variant="h6" gutterBottom>
          Security Settings
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Two-factor authentication"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Password expiry (90 days)"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Session Timeout (minutes)"
              type="number"
              defaultValue={30}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Failed Login Attempts"
              type="number"
              defaultValue={5}
            />
          </Grid>
        </Grid>
        
        <Button variant="contained" onClick={handleSave} sx={{ mt: 2 }}>
          Save Settings
        </Button>
      </Paper>
    </Box>
  );
}