import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Card, 
  CardContent, 
  Grid, 
  Paper, 
  Avatar,
  Button,
  ButtonGroup,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  TextField,
  MenuItem,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

// Enhanced data structure based on the dashboard images
const performanceData = {
  targetPurchases: 200000,
  actualPurchases: 150000,
  incentiveEarned: 5000,
  kpiPercentage: 75
};

export default function Customer() {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState('brandPerformance');
  const [viewMode, setViewMode] = useState('week');
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');

  // New state for API data
  const [brands, setBrands] = useState([]);
  const [skus, setSkus] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch brands
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) return;
    fetch('http://127.0.0.1:8000/api/brands/', {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(data => setBrands(data))
      .catch(() => setError('Failed to fetch brands'));
  }, []);

  // Fetch SKUs
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) return;
    fetch('http://127.0.0.1:8000/api/brands/skus/', {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(data => setSkus(data))
      .catch(() => setError('Failed to fetch SKUs'));
  }, []);

  // Fetch transactions (sales) with date filter
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) return;
    setLoading(true);
    let url = 'http://127.0.0.1:8000/api/promotions/transactions/';
    const params = [];
    if (fromDate) params.push(`date_from=${fromDate}`);
    if (toDate) params.push(`date_to=${toDate}`);
    if (params.length) url += '?' + params.join('&');
    fetch(url, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    })
      .then(res => res.json())
      .then(data => setTransactions(data))
      .catch(() => setError('Failed to fetch transactions'))
      .finally(() => setLoading(false));
  }, [fromDate, toDate]);

  // Helper: Aggregate sales by brand
  const brandPerformanceData = brands.map(brand => {
    // Filter transactions for this brand
    const brandTxns = transactions.filter(txn => txn.brand === brand.id || txn.brand === brand.name);
    // Example: sum sales for week1, week2, and month (replace with real logic as needed)
    const week1 = brandTxns.filter(t => t.week === 1).reduce((sum, t) => sum + (t.sales_value || 0), 0);
    const week2 = brandTxns.filter(t => t.week === 2).reduce((sum, t) => sum + (t.sales_value || 0), 0);
    const month = brandTxns.reduce((sum, t) => sum + (t.sales_value || 0), 0);
    return {
      brand: brand.name,
      week1,
      week2,
      month,
      logo: brand.logo || '',
    };
  });

  // Helper: Aggregate sales by SKU
  const skuPerformanceData = skus.map(sku => {
    const skuTxns = transactions.filter(txn => txn.sku === sku.id || txn.sku === sku.name);
    const week1 = skuTxns.filter(t => t.week === 1).reduce((sum, t) => sum + (t.sales_value || 0), 0);
    const week2 = skuTxns.filter(t => t.week === 2).reduce((sum, t) => sum + (t.sales_value || 0), 0);
    const month = skuTxns.reduce((sum, t) => sum + (t.sales_value || 0), 0);
    return {
      name: sku.name,
      week1,
      week2,
      month,
    };
  });

  // Helper: Weekly sales trends for chart
  const purchaseTrendsData = [];
  for (let week = 1; week <= 5; week++) {
    const weekSales = transactions.filter(t => t.week === week).reduce((sum, t) => sum + (t.sales_value || 0), 0);
    purchaseTrendsData.push({ week: `Week ${week}`, purchases: weekSales });
  }

  const formatCurrency = (amount) => {
    return `₦${amount.toLocaleString()}`;
  };

  const formatNumber = (num) => {
    return num.toLocaleString();
  };

  const getPerformanceColor = (percentage) => {
    if (percentage >= 80) return theme.palette.success.main;
    if (percentage >= 60) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  return (
    <Box sx={{ 
      width: '100%', 
      minHeight: '100vh', 
      bgcolor: theme.palette.background.default, 
      p: { xs: 2, md: 3 } 
    }}>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'flex-start',
        mb: 3,
        flexDirection: { xs: 'column', md: 'row' },
        gap: 2
      }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            Store Dashboard
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
            Welcome back, Ngozi!
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
            Outlet No: 2
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
            SalesRep: Olumide Olowu (080-124-4567)
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Distributor: Ayo Distributors (080-987-6543)
          </Typography>
        </Box>

        {/* Date Range Filters */}
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Box>
            <Typography variant="caption" sx={{ display: 'block', mb: 0.5 }}>
              From
            </Typography>
            <TextField
              size="small"
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
              sx={{ minWidth: 140 }}
            />
          </Box>
          <Box>
            <Typography variant="caption" sx={{ display: 'block', mb: 0.5 }}>
              To
            </Typography>
            <TextField
              size="small"
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              sx={{ minWidth: 140 }}
            />
          </Box>
        </Box>
      </Box>

      {/* Performance Summary Cards */}
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
        Performance
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Target Purchases
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                {formatCurrency(performanceData.targetPurchases)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Actual Purchases
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.success.main }}>
                {formatCurrency(performanceData.actualPurchases)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Incentive Earned
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.warning.main }}>
                {formatCurrency(performanceData.incentiveEarned)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                KPI versus Actual
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mr: 1 }}>
                  {performanceData.kpiPercentage}%
                </Typography>
                <Chip 
                  label={performanceData.kpiPercentage >= 75 ? 'Good' : 'Needs Improvement'} 
                  color={performanceData.kpiPercentage >= 75 ? 'success' : 'warning'}
                  size="small"
                />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={performanceData.kpiPercentage} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  backgroundColor: alpha(getPerformanceColor(performanceData.kpiPercentage), 0.1),
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: getPerformanceColor(performanceData.kpiPercentage)
                  }
                }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Tabs */}
      <Box sx={{ mb: 3 }}>
        <ButtonGroup variant="outlined" sx={{ mb: 2 }}>
          <Button 
            variant={activeTab === 'brandPerformance' ? 'contained' : 'outlined'}
            onClick={() => setActiveTab('brandPerformance')}
          >
            Brand Performance (Weekly)
          </Button>
          <Button 
            variant={activeTab === 'skuPerformance' ? 'contained' : 'outlined'}
            onClick={() => setActiveTab('skuPerformance')}
          >
            SKU Performance
          </Button>
          <Button 
            variant={activeTab === 'salesTrends' ? 'contained' : 'outlined'}
            onClick={() => setActiveTab('salesTrends')}
          >
            Weekly Sales Trends
          </Button>
        </ButtonGroup>
      </Box>

      {/* Content Based on Active Tab */}
      {activeTab === 'brandPerformance' && (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Brand Performance
              </Typography>
              <ButtonGroup size="small" variant="outlined">
                <Button 
                  variant={viewMode === 'week' ? 'contained' : 'outlined'}
                  onClick={() => setViewMode('week')}
                >
                  Week
                </Button>
                <Button 
                  variant={viewMode === 'month' ? 'contained' : 'outlined'}
                  onClick={() => setViewMode('month')}
                >
                  Month
                </Button>
              </ButtonGroup>
            </Box>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Brand</TableCell>
                    <TableCell align="right">Week 1</TableCell>
                    <TableCell align="right">Week 2</TableCell>
                    {viewMode === 'month' && <TableCell align="right">Month Total</TableCell>}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {brandPerformanceData.map((brand) => (
                    <TableRow key={brand.brand} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            src={brand.logo}
                            alt={brand.brand}
                            sx={{ width: 32, height: 32, mr: 2 }}
                          />
                          <Typography variant="body2" fontWeight={600}>
                            {brand.brand}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="right">{formatCurrency(brand.week1)}</TableCell>
                      <TableCell align="right">{formatCurrency(brand.week2)}</TableCell>
                      {viewMode === 'month' && (
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight={600}>
                            {formatCurrency(brand.month)}
                          </Typography>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {activeTab === 'skuPerformance' && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              SKU Performance
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>SKU</TableCell>
                    <TableCell align="right">Week 1</TableCell>
                    <TableCell align="right">Week 2</TableCell>
                    <TableCell align="right">Change</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {skuPerformanceData.map((sku, idx) => {
                    const change = sku.week1 === 0 ? 0 : ((sku.week2 - sku.week1) / sku.week1) * 100;
                    return (
                      <TableRow key={idx} hover>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {sku.name}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">{formatCurrency(sku.week1)}</TableCell>
                        <TableCell align="right">{formatCurrency(sku.week2)}</TableCell>
                        <TableCell align="right">
                          <Chip
                            label={`${change > 0 ? '+' : ''}${change.toFixed(1)}%`}
                            color={change > 0 ? 'success' : change < 0 ? 'error' : 'default'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {activeTab === 'salesTrends' && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Purchase Trends
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={purchaseTrendsData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="week" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(value)} />
                      <Line 
                        type="monotone" 
                        dataKey="purchases" 
                        stroke={theme.palette.primary.main}
                        strokeWidth={3}
                        dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Purchase Trends Summary
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.primary.main, mb: 1 }}>
                  {formatCurrency(150000)}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Last 14 Weeks: +1.5%
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Typography variant="body2" color="text.secondary">
                  Showing consistent growth trend with peak performance in Week 5
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
}