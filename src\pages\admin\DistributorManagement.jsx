import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Alert,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import apiService from '../../services/apiService';

export default function DistributorManagement() {
  const [distributors, setDistributors] = useState([]);
  const [territories, setTerritories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingDistributor, setEditingDistributor] = useState(null);
  const [message, setMessage] = useState({ type: '', text: '' });

  const [formData, setFormData] = useState({
    distributor_name: '',
    distributor_code: '',
    distributor_territory_name: '',
    distributor_owner_name: '',
    territory_manager: '',
    territory: ''
  });

  useEffect(() => {
    loadDistributors();
    loadTerritories();
  }, []);

  const loadDistributors = async () => {
    try {
      setLoading(true);
      const response = await apiService.getDistributors();
      setDistributors(response.results || response);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to load distributors' });
    } finally {
      setLoading(false);
    }
  };

  const loadTerritories = async () => {
    try {
      const response = await apiService.getTerritories();
      setTerritories(response.results || response);
    } catch (error) {
      console.error('Failed to load territories:', error);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      if (editingDistributor) {
        await apiService.updateDistributor(editingDistributor.id, formData);
        setMessage({ type: 'success', text: 'Distributor updated successfully!' });
      } else {
        await apiService.createDistributor(formData);
        setMessage({ type: 'success', text: 'Distributor created successfully!' });
      }
      
      setDialogOpen(false);
      resetForm();
      loadDistributors();
    } catch (error) {
      setMessage({ type: 'error', text: error.message || 'Operation failed' });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (distributor) => {
    setEditingDistributor(distributor);
    setFormData({
      distributor_name: distributor.distributor_name || '',
      distributor_code: distributor.distributor_code || '',
      distributor_territory_name: distributor.distributor_territory_name || '',
      distributor_owner_name: distributor.distributor_owner_name || '',
      territory_manager: distributor.territory_manager || '',
      territory: distributor.territory || ''
    });
    setDialogOpen(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this distributor?')) {
      try {
        setLoading(true);
        await apiService.deleteDistributor(id);
        setMessage({ type: 'success', text: 'Distributor deleted successfully!' });
        loadDistributors();
      } catch (error) {
        setMessage({ type: 'error', text: error.message || 'Delete failed' });
      } finally {
        setLoading(false);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      distributor_name: '',
      distributor_code: '',
      distributor_territory_name: '',
      distributor_owner_name: '',
      territory_manager: '',
      territory: ''
    });
    setEditingDistributor(null);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    resetForm();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" fontWeight={600} mb={1}>
            Distributor Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage distributors and their territories
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setDialogOpen(true)}
        >
          Add Distributor
        </Button>
      </Box>

      {message.text && (
        <Alert 
          severity={message.type} 
          sx={{ mb: 3 }}
          onClose={() => setMessage({ type: '', text: '' })}
        >
          {message.text}
        </Alert>
      )}

      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>Distributor</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Territory</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Owner</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Territory Manager</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {distributors.map((distributor) => (
                  <TableRow key={distributor.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon sx={{ mr: 2, color: 'primary.main' }} />
                        <Box>
                          <Typography variant="body2" fontWeight={600}>
                            {distributor.distributor_name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {distributor.distributor_code}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {distributor.distributor_territory_name}
                        </Typography>
                        {distributor.territory_name && (
                          <Chip 
                            label={distributor.territory_name} 
                            size="small" 
                            variant="outlined"
                            sx={{ mt: 0.5 }}
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {distributor.distributor_owner_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {distributor.territory_manager}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(distributor)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(distributor.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingDistributor ? 'Edit Distributor' : 'Add New Distributor'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="distributor_name"
                label="Distributor Name"
                fullWidth
                required
                value={formData.distributor_name}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="distributor_code"
                label="Distributor Code"
                fullWidth
                required
                value={formData.distributor_code}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="distributor_territory_name"
                label="Territory Name"
                fullWidth
                required
                value={formData.distributor_territory_name}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="distributor_owner_name"
                label="Owner Name"
                fullWidth
                required
                value={formData.distributor_owner_name}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="territory_manager"
                label="Territory Manager"
                fullWidth
                required
                value={formData.territory_manager}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Territory</InputLabel>
                <Select
                  name="territory"
                  value={formData.territory}
                  onChange={handleInputChange}
                  label="Territory"
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {territories.map((territory) => (
                    <MenuItem key={territory.id} value={territory.id}>
                      {territory.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Saving...' : (editingDistributor ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
