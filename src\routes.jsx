import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Auth & public pages
import Login from './pages/auth/Login';           // ✅ Login using phone number
import Register from './pages/auth/Register';     // ✅ Customer registration
import VerifyOtp from './pages/auth/VerifyOtp';   // ✅ OTP page after login

// Dashboard pages (protected)
import Dashboard from './pages/dashboard/Dashboard';
import BrandList from './pages/brands/BrandList';
import BrandDetail from './pages/brands/BrandDetail';
import Profile from './pages/user/Profile';
import Settings from './pages/user/Settings';

// Layout for dashboard pages
import MainDashboardLayout from './layouts/MainDashboardLayout';

export default function AppRoutes() {
  return (
    <Routes>
      {/* ===================== PUBLIC/AUTH ROUTES ===================== */}
      <Route path="/login" element={<Login />} />               {/* Login via phone number only */}
      <Route path="/register" element={<Register />} />         {/* Customer registration */}
      <Route path="/verify-otp" element={<VerifyOtp />} />      {/* Page to enter OTP after login */}

      {/* ===================== PROTECTED ROUTES WITH LAYOUT ===================== */}
      <Route element={<MainDashboardLayout />}>
        {/* User Account Pages */}
        <Route path="/profile" element={<Profile />} />
        <Route path="/settings" element={<Settings />} />

        {/* Main Dashboard Pages */}
        <Route path="/dashboard" element={<Dashboard />} />

        {/* Brand Management */}
        <Route path="/brands" element={<BrandList />} />
        <Route path="/brands/:brandId" element={<BrandDetail />} />
      </Route>

      {/* ===================== REDIRECT ROOT TO DASHBOARD ===================== */}
      <Route path="/" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
}
