# Project Innov8 Frontend

A modern React application for brand management and analytics.

## Project Overview

Project Innov8 is a comprehensive brand management platform built with React, Material UI, and Vite. It provides an intuitive interface for managing brands, viewing analytics, and configuring user settings.

## Features

- Dashboard with key metrics and analytics
- Brand management and categorization
- User profile management
- Application settings
- Responsive design for all devices

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-organization/project-innov8-frontend.git
cd project-innov8-frontend
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
```

4. Open your browser and navigate to:
```
http://localhost:5173
```

## Important Guidelines for Developers

- **DO NOT install new modules** without prior approval from the project lead
- **DO NOT modify the project setup or configuration files** (vite.config.js, package.json, etc.)
- **NEVER push directly to the main branch** - always create your own feature branch and submit a pull request
- Always name your branch with your initials followed by the feature name (e.g., `ea/add-brand-filter`)
- Focus ONLY on the feature you are assigned to work on
- Follow the coding standards and patterns established in the project
- Make sure your code is properly tested before submitting a pull request

## Complete Project Structure

```
project-innov8-frontend/
├── node_modules/        # Dependencies
├── public/              # Static files
│   ├── favicon.ico      # Favicon
│   ├── robots.txt       # Robots file
│   └── index.html       # HTML template
├── src/                 # Source code
│   ├── assets/          # Images, fonts, etc.
│   │   ├── images/      # Image files
│   │   ├── icons/       # Icon files
│   │   └── fonts/       # Font files
│   ├── components/      # Reusable UI components
│   │   ├── common/      # Common components used across the app
│   │   ├── forms/       # Form components
│   │   ├── charts/      # Chart components
│   │   └── tables/      # Table components
│   ├── layouts/         # Layout components
│   │   ├── DashboardLayout.jsx  # Main dashboard layout
│   │   ├── AuthLayout.jsx       # Authentication layout
│   │   └── ErrorLayout.jsx      # Error page layout
│   ├── pages/           # Page components
│   │   ├── brands/      # Brand-related pages
│   │   │   ├── BrandList.jsx    # List of brands
│   │   │   ├── BrandDetail.jsx  # Brand details
│   │   │   └── BrandForm.jsx    # Create/edit brand form
│   │   ├── dashboard/   # Dashboard pages
│   │   │   ├── Dashboard.jsx    # Main dashboard
│   │   │   ├── Analytics.jsx    # Analytics dashboard
│   │   │   └── Reports.jsx      # Reports dashboard
│   │   ├── profile/     # Profile pages
│   │   │   ├── Profile.jsx      # User profile
│   │   │   └── EditProfile.jsx  # Edit profile form
│   │   ├── settings/    # Settings pages
│   │   │   ├── Settings.jsx     # Main settings
│   │   │   ├── Security.jsx     # Security settings
│   │   │   └── Notifications.jsx # Notification settings
│   │   ├── auth/        # Authentication pages
│   │   │   ├── Login.jsx        # Login page
│   │   │   ├── Register.jsx     # Registration page
│   │   │   └── ForgotPassword.jsx # Password recovery
│   │   └── errors/      # Error pages
│   │       ├── NotFound.jsx     # 404 page
│   │       └── ServerError.jsx  # 500 page
│   ├── services/        # API services
│   │   ├── api.js       # API client setup
│   │   ├── authService.js # Authentication service
│   │   ├── brandService.js # Brand service
│   │   └── userService.js # User service
│   ├── utils/           # Utility functions
│   │   ├── formatters.js # Data formatters
│   │   ├── validators.js # Form validators
│   │   └── helpers.js   # Helper functions
│   ├── hooks/           # Custom React hooks
│   │   ├── useAuth.js   # Authentication hook
│   │   └── useFetch.js  # Data fetching hook
│   ├── context/         # React context providers
│   │   ├── AuthContext.jsx # Authentication context
│   │   └── ThemeContext.jsx # Theme context
│   ├── theme/           # Theme configuration
│   │   ├── index.js     # Theme entry point
│   │   ├── palette.js   # Color palette
│   │   └── typography.js # Typography settings
│   ├── App.jsx          # Main App component
│   ├── index.jsx        # Entry point
│   └── routes.jsx       # Route definitions
├── .eslintrc.js         # ESLint configuration
├── .gitignore           # Git ignore file
├── .prettierrc          # Prettier configuration
├── CONTRIBUTING.md      # Contribution guidelines
├── LICENSE              # License file
├── package.json         # Project dependencies and scripts
├── README.md            # Project overview
└── vite.config.js       # Vite configuration
```

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the app for production
- `npm run preview` - Preview the production build locally
- `npm run lint` - Run ESLint to check code quality
- `npm run test` - Run tests

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## Deployment

The application is automatically deployed to our staging environment when changes are merged to the `develop` branch, and to production when merged to `main`.

## Built With

- [React](https://reactjs.org/) - UI library
- [Material UI](https://mui.com/) - Component library
- [Vite](https://vitejs.dev/) - Build tool
- [React Router](https://reactrouter.com/) - Routing

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Thanks to all contributors who have helped shape this project
- Special thanks to the design team for their UI/UX input

---

Documentation by Emmanuel Adubi.



