import { Outlet, Routes, Route } from "react-router-dom";
import AdminProfile from "../pages/admin/AdminProfile";
import DistributorManagement from "../pages/admin/DistributorManagement";
import DSRManagement from "../pages/admin/DSRManagement";
import CustomerManagement from "../pages/admin/CustomerManagement";
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  CssBaseline,
  useTheme,
  useMediaQuery,
  Grid,
  Card,
  CardContent,
  ThemeProvider,
  createTheme,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Menu
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  Campaign as CampaignIcon,
  Redeem as RedeemIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  Search as SearchIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  AccountCircle as AccountCircleIcon,
  Logout as LogoutIcon,
  Person as PersonIcon
} from "@mui/icons-material";
import adminAuthService from "../services/adminAuthService";
import React, { useState, useMemo } from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Tooltip,
  Legend
} from "chart.js";
import UploadCsvForm from "./UploadCsvForm"; // Import the CSV upload component

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Tooltip,
  Legend
);

const drawerWidth = 240;

const navItems = [
  { label: "Dashboard", icon: <DashboardIcon /> },
  { label: "Customers", icon: <PersonIcon /> },
  { label: "Campaigns", icon: <CampaignIcon /> },
  { label: "Rewards", icon: <RedeemIcon /> },
  { label: "Members", icon: <PeopleIcon /> },
  { label: "Distributors", icon: <PeopleIcon /> },
  { label: "DSRs", icon: <PersonIcon /> },
  { label: "Settings", icon: <SettingsIcon /> }
];

// Loyalty program analytics data
const loyaltyStatsData = {
  labels: ["North", "South", "East", "West", "Central"],
  datasets: [
    {
      label: "Loyalty Points",
      backgroundColor: "#26a69a",
      borderRadius: 6,
      data: [4200, 3800, 4600, 3200, 4100]
    }
  ]
};

// Regional Sales Managers data
const regionalManagers = [
  { region: "North", gsmName: "Emily Carter", storeCount: 320, rewardsIssued: 4200 },
  { region: "South", gsmName: "David Lee", storeCount: 280, rewardsIssued: 3800 },
  { region: "East", gsmName: "Sophie Clark", storeCount: 350, rewardsIssued: 4600 },
  { region: "West", gsmName: "Ethan Brown", storeCount: 220, rewardsIssued: 3200 },
  { region: "Central", gsmName: "Olivia Green", storeCount: 310, rewardsIssued: 4100 }
];

export default function LoyaltyDashboard() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [active, setActive] = useState(0);
  const [dateFilter, setDateFilter] = useState("This Month");
  const [campaignType, setCampaignType] = useState("All Campaigns");
  const [darkMode, setDarkMode] = useState(false); // Add dark mode state
  const [profileMenuAnchor, setProfileMenuAnchor] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);

  // Load current user on component mount
  React.useEffect(() => {
    const user = adminAuthService.getUser();
    setCurrentUser(user);
  }, []);

  // Profile menu handlers
  const handleProfileMenuOpen = (event) => {
    setProfileMenuAnchor(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setProfileMenuAnchor(null);
  };

  const handleLogout = () => {
    adminAuthService.logout();
  };

  const handleViewProfile = () => {
    setActive(8); // Set to profile view
    handleProfileMenuClose();
  };

  // Function to render content based on active navigation
  const renderContent = () => {
    switch (active) {
      case 0:
        return renderDashboard();
      case 1:
        return <CustomerManagement />;
      case 2:
        return renderCampaigns();
      case 3:
        return renderRewards();
      case 4:
        return renderMembers();
      case 5:
        return <DistributorManagement />;
      case 6:
        return <DSRManagement />;
      case 7:
        return renderSettings();
      case 8:
        return <AdminProfile />;
      default:
        return renderDashboard();
    }
  };

  // Sample customers data with new fields
  const customersData = [
    {
      id: 1,
      customer_code: "CUST001",
      username: "ngozi_okafor",
      email: "<EMAIL>",
      phone_number: "+234-************",
      channel: "OMLS",
      customer_type: "gold",
      physical_address: "123 Main Street, Lagos",
      distributor_name: "ABC Distribution Ltd",
      dsr_name: "Mike Johnson",
      status: "Active",
      created_at: "2024-01-15"
    },
    {
      id: 2,
      customer_code: "CUST002",
      username: "emeka_nnadi",
      email: "<EMAIL>",
      phone_number: "+234-************",
      channel: "OMLS",
      customer_type: "silver",
      physical_address: "456 Market Road, Abuja",
      distributor_name: "XYZ Trading Co",
      dsr_name: "Sarah Wilson",
      status: "Active",
      created_at: "2024-02-20"
    },
    {
      id: 3,
      customer_code: "CUST003",
      username: "fatima_abdullahi",
      email: "<EMAIL>",
      phone_number: "+234-************",
      channel: "OMLS",
      customer_type: "bronze",
      physical_address: "789 Trade Center, Kano",
      distributor_name: "Northern Distributors",
      dsr_name: "Ahmed Hassan",
      status: "Inactive",
      created_at: "2024-01-10"
    },
    {
      id: 4,
      customer_code: "CUST004",
      username: "chinedu_okwu",
      email: "<EMAIL>",
      phone_number: "+234-************",
      channel: "OMLS",
      customer_type: "gold",
      physical_address: "321 Business District, Port Harcourt",
      distributor_name: "South Coast Distribution",
      dsr_name: "Grace Eze",
      status: "Active",
      created_at: "2024-03-05"
    },
    {
      id: 5,
      customer_code: "CUST005",
      username: "aisha_mohammed",
      email: "<EMAIL>",
      phone_number: "+234-************",
      channel: "OMLS",
      customer_type: "silver",
      physical_address: "654 Commercial Avenue, Kaduna",
      distributor_name: "Central Nigeria Distributors",
      dsr_name: "Ibrahim Musa",
      status: "Active",
      created_at: "2024-02-28"
    },
  ];

  // Dashboard content (existing dashboard)
  const renderDashboard = () => (
    <>
      {/* Dashboard Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" fontWeight={600} color="text.primary" sx={{ mb: 1 }}>
          Dashboard
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Overview of loyalty program performance
        </Typography>
      </Box>

      {/* Customers List Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
          Recent Customers
        </Typography>
        <Card>
          <CardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 600 }}>Customer</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Contact</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Type & Channel</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Distributor</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>DSR</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Created</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {customersData.map((customer) => (
                    <TableRow key={customer.id} hover>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight={600}>
                            {customer.username}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {customer.customer_code}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">{customer.phone_number}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {customer.email}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                            {customer.customer_type}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {customer.channel}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {customer.distributor_name || 'Not Assigned'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {customer.dsr_name || 'Not Assigned'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            px: 1,
                            py: 0.5,
                            borderRadius: 1,
                            backgroundColor: customer.status === "Active" ? "#e8f5e8" : "#ffeaa7",
                            color: customer.status === "Active" ? "#2d5a2d" : "#b8860b",
                            display: "inline-block",
                            fontSize: "0.75rem",
                            fontWeight: 600
                          }}
                        >
                          {customer.status}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(customer.created_at).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>

      {/* Filters */}
      <Box sx={{ mb: 3, display: "flex", gap: 2, flexWrap: "wrap" }}>
        <FormControl size="small" sx={{ minWidth: 140 }}>
          <InputLabel>Date</InputLabel>
          <Select
            value={dateFilter}
            label="Date"
            onChange={(e) => setDateFilter(e.target.value)}
          >
            <MenuItem value="This Week">This Week</MenuItem>
            <MenuItem value="This Month">This Month</MenuItem>
            <MenuItem value="This Quarter">This Quarter</MenuItem>
          </Select>
        </FormControl>
        <FormControl size="small" sx={{ minWidth: 160 }}>
          <InputLabel>Campaign Type</InputLabel>
          <Select
            value={campaignType}
            label="Campaign Type"
            onChange={(e) => setCampaignType(e.target.value)}
          >
            <MenuItem value="All Campaigns">All Campaigns</MenuItem>
            <MenuItem value="Seasonal">Seasonal</MenuItem>
            <MenuItem value="Product Launch">Product Launch</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                1200
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Total Stores
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                50
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Campaigns
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                15000
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Rewards
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                85%
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Engagement
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Regional Performance, Loyalty Stats by Region, and Regional Sales Managers side by side */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Regional Performance
              </Typography>
              <Box
                sx={{
                  height: 300,
                  background: `linear-gradient(135deg, ${customTheme.palette.primary.light}40, ${customTheme.palette.primary.main}60)`,
                  borderRadius: 2,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  position: "relative",
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 0C13.4315 0 0 13.4315 0 30C0 46.5685 13.4315 60 30 60C46.5685 60 60 46.5685 60 30C60 13.4315 46.5685 0 30 0ZM30 55C16.1929 55 5 43.8071 5 30C5 16.1929 16.1929 5 30 5C43.8071 5 55 16.1929 55 30C55 43.8071 43.8071 55 30 55Z' fill='white' fill-opacity='0.1'/%3E%3C/svg%3E")`,
                  backgroundSize: "30px 30px"
                }}
              >
                <Typography variant="h6" color="white" fontWeight={600}>
                  🗺️ US Regional Map Visualization
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Loyalty Stats by Region
              </Typography>
              <Box sx={{ height: 300 }}>
                <Bar
                  data={loyaltyStatsData}
                  options={{
                    plugins: {
                      legend: { display: false },
                      tooltip: {
                        backgroundColor: customTheme.palette.background.paper,
                        titleColor: customTheme.palette.text.primary,
                        bodyColor: customTheme.palette.text.secondary,
                        borderColor: customTheme.palette.grey[300],
                        borderWidth: 1
                      }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      x: {
                        ticks: { color: customTheme.palette.text.secondary },
                        grid: { display: false }
                      },
                      y: {
                        ticks: { color: customTheme.palette.text.secondary },
                        grid: { color: customTheme.palette.grey[200] }
                      }
                    }
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Regional Sales Managers
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        Region
                      </TableCell>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        GSM Name
                      </TableCell>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        Store Count
                      </TableCell>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        Rewards Issued
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {regionalManagers.map((manager) => (
                      <TableRow key={manager.region} hover>
                        <TableCell sx={{ fontWeight: 500 }}>{manager.region}</TableCell>
                        <TableCell>{manager.gsmName}</TableCell>
                        <TableCell>{manager.storeCount.toLocaleString()}</TableCell>
                        <TableCell>{manager.rewardsIssued.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* CSV Upload Section for Admin */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
          Upload Data via CSV
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6} lg={4}>
            <UploadCsvForm label="Customers" endpoint="/api/users/customers/upload-excel/" />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <UploadCsvForm label="Distributors" endpoint="/api/users/distributors/upload-excel/" />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <UploadCsvForm label="DSRs" endpoint="/api/users/dsrs/upload-excel/" />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <UploadCsvForm label="Brands" endpoint="/api/brands/upload-excel/" />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <UploadCsvForm label="SKUs" endpoint="/api/brands/skus/upload-excel/" />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <UploadCsvForm label="Schemes" endpoint="/api/promotions/schemes/upload-excel/" />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <UploadCsvForm label="Promotions" endpoint="/api/promotions/upload-excel/" />
          </Grid>
        </Grid>
      </Box>
    </>
  );

  // Campaigns content - showing latest promotions, schemes, and notifications
  const renderCampaigns = () => (
    <Box>
      <Typography variant="h5" fontWeight={600} color="text.primary" sx={{ mb: 1 }}>
        Campaigns
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Latest information on promotions, new schemes, and customer notifications
      </Typography>

      {/* Campaign Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                12
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Active Campaigns
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                8
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                New Schemes
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                156
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Notifications Sent
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                89%
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Engagement Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Campaigns */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
                Recent Promotions
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ p: 2, border: "1px solid #e0e0e0", borderRadius: 1 }}>
                  <Typography variant="subtitle2" fontWeight={600}>
                    Summer Sale 2024
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    20% off on all Unilever products
                  </Typography>
                  <Typography variant="caption" color="success.main">
                    Active until July 31, 2024
                  </Typography>
                </Box>
                <Box sx={{ p: 2, border: "1px solid #e0e0e0", borderRadius: 1 }}>
                  <Typography variant="subtitle2" fontWeight={600}>
                    New Customer Bonus
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Extra 500 points for first purchase
                  </Typography>
                  <Typography variant="caption" color="success.main">
                    Ongoing
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
                New Schemes Added
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ p: 2, border: "1px solid #e0e0e0", borderRadius: 1 }}>
                  <Typography variant="subtitle2" fontWeight={600}>
                    Loyalty Plus Program
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Enhanced rewards for premium customers
                  </Typography>
                  <Typography variant="caption" color="info.main">
                    Added July 15, 2024
                  </Typography>
                </Box>
                <Box sx={{ p: 2, border: "1px solid #e0e0e0", borderRadius: 1 }}>
                  <Typography variant="subtitle2" fontWeight={600}>
                    Referral Rewards
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Earn points for successful referrals
                  </Typography>
                  <Typography variant="caption" color="info.main">
                    Added July 10, 2024
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  // Sample rewards data
  const rewardsData = [
    { id: 1, customerName: "Ngozi Okafor", customerId: "CUST001", currentPoints: 2450, totalEarned: 5670, totalRedeemed: 3220, lastActivity: "2024-07-15", tier: "Gold" },
    { id: 2, customerName: "Emeka Nnadi", customerId: "CUST002", currentPoints: 1890, totalEarned: 4320, totalRedeemed: 2430, lastActivity: "2024-07-14", tier: "Silver" },
    { id: 3, customerName: "Fatima Abdullahi", customerId: "CUST003", currentPoints: 3120, totalEarned: 6890, totalRedeemed: 3770, lastActivity: "2024-07-13", tier: "Platinum" },
    { id: 4, customerName: "Chinedu Okwu", customerId: "CUST004", currentPoints: 1650, totalEarned: 3980, totalRedeemed: 2330, lastActivity: "2024-07-12", tier: "Silver" },
    { id: 5, customerName: "Aisha Mohammed", customerId: "CUST005", currentPoints: 2280, totalEarned: 5450, totalRedeemed: 3170, lastActivity: "2024-07-11", tier: "Gold" },
  ];

  // Rewards content
  const renderRewards = () => (
    <Box>
      <Typography variant="h5" fontWeight={600} color="text.primary" sx={{ mb: 1 }}>
        Rewards
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        List of customers with their respective rewards and points
      </Typography>

      {/* Rewards Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                12,390
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Total Points Issued
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                8,920
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Points Redeemed
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                3,470
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Active Points
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                72%
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Redemption Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Customer Rewards Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
            Customer Rewards Overview
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>Customer</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Tier</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Current Points</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Total Earned</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Total Redeemed</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Last Activity</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {rewardsData.map((reward) => (
                  <TableRow key={reward.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight={600}>
                          {reward.customerName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {reward.customerId}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          backgroundColor:
                            reward.tier === "Platinum" ? "#e8f4fd" :
                            reward.tier === "Gold" ? "#fff8e1" : "#f3e5f5",
                          color:
                            reward.tier === "Platinum" ? "#1565c0" :
                            reward.tier === "Gold" ? "#f57c00" : "#7b1fa2",
                          display: "inline-block",
                          fontSize: "0.75rem",
                          fontWeight: 600
                        }}
                      >
                        {reward.tier}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight={600} color="primary.main">
                        {reward.currentPoints.toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell>{reward.totalEarned.toLocaleString()}</TableCell>
                    <TableCell>{reward.totalRedeemed.toLocaleString()}</TableCell>
                    <TableCell>{reward.lastActivity}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );

  // Sample staff members data
  const staffData = [
    { id: 1, name: "John Adebayo", role: "Regional Sales Manager", email: "<EMAIL>", phone: "+234-************", region: "Lagos", status: "Active", joinDate: "2023-01-15" },
    { id: 2, name: "Sarah Okonkwo", role: "Area Sales Manager", email: "<EMAIL>", phone: "+234-************", region: "Abuja", status: "Active", joinDate: "2023-03-20" },
    { id: 3, name: "Ahmed Hassan", role: "Territory Manager", email: "<EMAIL>", phone: "+234-************", region: "Kano", status: "Active", joinDate: "2023-02-10" },
    { id: 4, name: "Grace Eze", role: "Sales Representative", email: "<EMAIL>", phone: "+234-************", region: "Port Harcourt", status: "Inactive", joinDate: "2023-04-05" },
    { id: 5, name: "Ibrahim Musa", role: "District Manager", email: "<EMAIL>", phone: "+234-************", region: "Kaduna", status: "Active", joinDate: "2023-05-12" },
  ];

  // Members content
  const renderMembers = () => (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
        <Box>
          <Typography variant="h5" fontWeight={600} color="text.primary" sx={{ mb: 1 }}>
            Members
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Staff members and Unilever managers management
          </Typography>
        </Box>
        <Button variant="contained" sx={{ backgroundColor: customTheme.palette.primary.main, color: "white" }}>
          Add New Member
        </Button>
      </Box>

      {/* Staff Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                25
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Total Staff
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                22
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Active Members
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                5
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Regions Covered
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: "center", py: 2.5 }}>
              <Typography variant="h4" fontWeight={700} color="text.primary">
                8
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Managers
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Staff Members Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
            Staff Members
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>Name</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Role</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Contact</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Region</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Join Date</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {staffData.map((staff) => (
                  <TableRow key={staff.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight={600}>
                        {staff.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {staff.role}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">{staff.phone}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {staff.email}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{staff.region}</TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          backgroundColor: staff.status === "Active" ? "#e8f5e8" : "#ffeaa7",
                          color: staff.status === "Active" ? "#2d5a2d" : "#b8860b",
                          display: "inline-block",
                          fontSize: "0.75rem",
                          fontWeight: 600
                        }}
                      >
                        {staff.status}
                      </Box>
                    </TableCell>
                    <TableCell>{staff.joinDate}</TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", gap: 1 }}>
                        <Button size="small" variant="outlined">
                          Edit
                        </Button>
                        <Button size="small" variant="outlined" color="error">
                          Delete
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );

  // Settings content
  const renderSettings = () => (
    <Box>
      <Typography variant="h5" fontWeight={600} color="text.primary" sx={{ mb: 1 }}>
        Settings
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        System configuration and preferences
      </Typography>
      <Typography variant="body1">
        Settings page content will be implemented here...
      </Typography>
    </Box>
  );

  // Black & White only theme, but allow toggling between light and dark
  const customTheme = useMemo(
    () =>
      createTheme({
        palette: {
          mode: darkMode ? "dark" : "light",
          primary: {
            main: "#000",
            light: "#222",
            dark: "#000"
          },
          secondary: {
            main: "#fff",
            light: "#fff",
            dark: "#eee"
          },
          background: {
            default: darkMode ? "#000" : "#fff",
            paper: darkMode ? "#111" : "#fff"
          },
          text: {
            primary: darkMode ? "#fff" : "#000",
            secondary: darkMode ? "#ccc" : "#222"
          },
          grey: {
            100: "#f5f5f5",
            200: "#e0e0e0",
            300: "#bdbdbd"
          }
        },
        typography: {
          fontFamily: "Inter, Roboto, Arial, sans-serif",
          fontWeightBold: 700,
          fontWeightMedium: 600,
          fontWeightRegular: 400,
          h6: {
            fontWeight: 600
          }
        },
        components: {
          MuiCard: {
            styleOverrides: {
              root: {
                borderRadius: 12,
                boxShadow: "0 2px 12px rgba(0,0,0,0.08)"
              }
            }
          }
        }
      }),
    [darkMode]
  );

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const drawer = (
    <Box sx={{ height: "100%", background: customTheme.palette.background.paper }}>
      <Box sx={{ p: 3, textAlign: "left", borderBottom: `1px solid ${customTheme.palette.grey[200]}` }}>
        <Typography variant="h6" fontWeight={700} color="text.primary">
          National Manager
        </Typography>
      </Box>
      <List sx={{ pt: 2 }}>
        {navItems.map((item, idx) => (
          <ListItem key={item.label} disablePadding sx={{ px: 1 }}>
            <ListItemButton
              selected={active === idx}
              onClick={() => setActive(idx)}
              sx={{
                borderRadius: 2,
                mx: 1,
                mb: 0.5,
                color: customTheme.palette.text.primary,
                "&.Mui-selected": {
                  background: customTheme.palette.primary.main,
                  color: "white",
                  "&:hover": {
                    background: customTheme.palette.primary.dark
                  }
                },
                "&:hover": {
                  background: customTheme.palette.grey[100]
                }
              }}
            >
              <ListItemIcon sx={{ 
                color: "inherit",
                minWidth: 40
              }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText 
                primary={item.label}
                primaryTypographyProps={{
                  fontSize: "0.9rem",
                  fontWeight: 500
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <ThemeProvider theme={customTheme}>
      <CssBaseline />
      <Box sx={{ display: "flex", minHeight: "100vh", background: customTheme.palette.background.default }}>
        <AppBar
          position="fixed"
          sx={{
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            ml: { sm: `${drawerWidth}px` },
            background: customTheme.palette.background.paper,
            color: customTheme.palette.text.primary,
            boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
            borderBottom: `1px solid ${customTheme.palette.grey[200]}`
          }}
        >
          <Toolbar>
            {isMobile && (
              <IconButton
                color="inherit"
                edge="start"
                onClick={() => setMobileOpen(!mobileOpen)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 600 }}>
              Loyalty Program
            </Typography>
            {/* Theme Selector */}
            <IconButton
              sx={{ mr: 2 }}
              onClick={() => setDarkMode((prev) => !prev)}
              color="inherit"
              aria-label="toggle theme"
            >
              {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
            </IconButton>
            <IconButton sx={{ mr: 1, color: customTheme.palette.text.secondary }}>
              <SearchIcon />
            </IconButton>
            <IconButton sx={{ mr: 2, color: customTheme.palette.text.secondary }}>
              <NotificationsIcon />
            </IconButton>

            {/* Profile Menu */}
            <IconButton
              onClick={handleProfileMenuOpen}
              sx={{ p: 0 }}
            >
              <Avatar sx={{
                bgcolor: customTheme.palette.primary.main,
                width: 36,
                height: 36,
                color: customTheme.palette.getContrastText(customTheme.palette.primary.main)
              }}>
                {currentUser?.username?.charAt(0).toUpperCase() || 'A'}
              </Avatar>
            </IconButton>

            <Menu
              anchorEl={profileMenuAnchor}
              open={Boolean(profileMenuAnchor)}
              onClose={handleProfileMenuClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <MenuItem onClick={handleViewProfile}>
                <PersonIcon sx={{ mr: 1 }} />
                Profile
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <LogoutIcon sx={{ mr: 1 }} />
                Logout
              </MenuItem>
            </Menu>
          </Toolbar>
        </AppBar>

        <Box
          component="nav"
          sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        >
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={() => setMobileOpen(false)}
            ModalProps={{ keepMounted: true }}
            sx={{
              display: { xs: "block", sm: "none" },
              "& .MuiDrawer-paper": {
                boxSizing: "border-box",
                width: drawerWidth,
                background: customTheme.palette.background.paper
              }
            }}
          >
            {drawer}
          </Drawer>
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: "none", sm: "block" },
              "& .MuiDrawer-paper": {
                boxSizing: "border-box",
                width: drawerWidth,
                background: customTheme.palette.background.paper,
                borderRight: `1px solid ${customTheme.palette.grey[200]}`
              }
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: { xs: 2, sm: 3 },
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            minHeight: "100vh"
          }}
        >
          <Toolbar />

          {/* Render content based on active navigation */}
          {renderContent()}

          {/* Nested routes */}
          <Outlet />
        </Box>
      </Box>
    </ThemeProvider>
  );
}