import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, TextField, Button, Typography, InputAdornment } from '@mui/material';
import PhoneAndroidIcon from '@mui/icons-material/PhoneAndroid';
import LockIcon from '@mui/icons-material/Lock';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export default function Login() {
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtp, setShowOtp] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    const trimmed = phone.trim();
    if (!/^\d{10,}$/.test(trimmed)) {
      setError('Enter a valid phone number with at least 10 digits');
      return;
    }
    setLoading(true);
    try {
      // Call backend to request OTP
      const res = await fetch(`${API_BASE_URL}/api/users/auth/request-otp/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone_number: trimmed })
      });
      if (!res.ok) {
        const err = await res.json();
        setError(err.detail || 'Failed to send OTP');
        setLoading(false);
        return;
      }
      // Show OTP input after successful OTP request
      setPhone(trimmed); // keep phone for verification
      setShowOtp(true);
      setError('');
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    setError('');
    if (!otp.trim()) {
      setError('Enter the OTP sent to your phone');
      return;
    }
    setLoading(true);
    try {
      // Call backend to verify OTP
      const res = await fetch(`${API_BASE_URL}/api/users/auth/verify-otp/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone_number: phone, otp: otp.trim() })
      });
      const data = await res.json();
      if (!res.ok) {
        setError(data.detail || 'Invalid OTP');
        setLoading(false);
        return;
      }
      // Store token and redirect to customer dashboard with phone number
      localStorage.setItem('token', data.token);
      navigate(`/customer?phone=${encodeURIComponent(phone)}`);
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        width: '100vw',
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f4f6f8',
      }}
    >
      <Box
        component="form"
        onSubmit={showOtp ? handleVerifyOtp : handleSubmit}
        sx={{
          width: '100%',
          maxWidth: 400,
          p: 4,
          bgcolor: 'white',
          borderRadius: 2,
          boxShadow: 3,
        }}
      >
        <Typography variant="h5" textAlign="center" mb={3} fontWeight="bold">
          {showOtp ? 'Enter OTP' : 'Login'}
        </Typography>

        {!showOtp ? (
          <TextField
            label="Phone Number"
            type="tel"
            fullWidth
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <PhoneAndroidIcon />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 3 }}
            error={!!error}
            helperText={error}
          />
        ) : (
          <TextField
            label="OTP"
            type="text"
            fullWidth
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LockIcon />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 3 }}
            error={!!error}
            helperText={error}
          />
        )}

        <Button
          type="submit"
          variant="contained"
          fullWidth
          size="large"
          startIcon={<LockIcon />}
          disabled={loading}
        >
          {loading ? (showOtp ? 'Verifying OTP...' : 'Sending OTP...') : (showOtp ? 'Verify OTP' : 'Send OTP')}
        </Button>
      </Box>
    </Box>
  );
}
