import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import adminAuthService from '../services/adminAuthService';

export default function ProtectedAdminRoute({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      // Check if user has token and user data
      const hasAuth = adminAuthService.isAuthenticated();

      if (!hasAuth) {
        console.log('No authentication found');
        setIsAuthenticated(false);
        setLoading(false);
        return;
      }

      // Get user data to check admin privileges
      const user = adminAuthService.getUser();
      console.log('Checking admin privileges for user:', user);

      // Check if user has admin privileges
      const userRole = user?.role?.display_name || user?.role?.name || user?.role || 'Unknown';
      const isStaff = user?.is_staff || user?.is_superuser || false;

      // Allow access if user is staff/superuser OR has admin-like roles
      const allowedRoles = ['Administrator', 'National Manager', 'Regional Manager', 'ADMIN', 'Admin', 'admin'];
      const hasAdminRole = allowedRoles.some(role =>
        userRole && userRole.toLowerCase().includes(role.toLowerCase())
      );

      if (!isStaff && !hasAdminRole) {
        console.log('User does not have admin privileges:', { userRole, isStaff });
        setIsAuthenticated(false);
        setLoading(false);
        return;
      }

      // Verify token with backend (optional - comment out if causing issues)
      try {
        const isValid = await adminAuthService.verifyToken();
        setIsAuthenticated(isValid);
      } catch (verifyError) {
        console.log('Token verification failed, but allowing access based on stored data');
        setIsAuthenticated(true); // Allow access if we have valid stored data
      }
    } catch (error) {
      console.error('Authentication check failed:', error);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          gap: 2
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Verifying authentication...
        </Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/admin/login" replace />;
  }

  return children;
}
