// Test file to verify CSV template generation
// This can be used to test the template functionality

const csvTemplates = {
  'Customers': {
    headers: ['customer_code', 'customer_name', 'phone_number', 'email', 'channel', 'address', 'state_city', 'town'],
    sampleData: [
      ['CUST001', '<PERSON>', '+234-************', '<EMAIL>', 'Retail', '123 Main Street', 'Lagos', 'Ikeja'],
      ['CUST002', '<PERSON>', '+234-************', '<EMAIL>', 'Wholesale', '456 Market Road', 'Abuja', 'Garki']
    ]
  },
  'Brands': {
    headers: ['brand_name', 'brand_code', 'category', 'description', 'status'],
    sampleData: [
      ['Lipton', 'LIP001', 'Beverages', 'Tea and beverage products', 'Active'],
      ['Dove', 'DOV001', 'Personal Care', 'Beauty and personal care products', 'Active']
    ]
  }
};

// Function to generate CSV content
function generateCSV(templateName) {
  const template = csvTemplates[templateName];
  if (!template) return null;
  
  const csvContent = [
    template.headers.join(','),
    ...template.sampleData.map(row => row.join(','))
  ].join('\n');
  
  return csvContent;
}

// Test the function
console.log('Customers CSV Template:');
console.log(generateCSV('Customers'));
console.log('\nBrands CSV Template:');
console.log(generateCSV('Brands'));

export { csvTemplates, generateCSV };
