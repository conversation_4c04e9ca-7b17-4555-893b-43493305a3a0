const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

class AdminAuthService {
  constructor() {
    this.tokenKey = 'adminToken';
    this.userKey = 'adminUser';
  }

  // Login admin user
  async login(username, password) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/auth/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Login failed');
      }

      // Store token and user info
      this.setToken(data.token);
      this.setUser(data.user);

      return data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Logout admin user
  logout() {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
    // Redirect to admin login
    window.location.href = '/admin/login';
  }

  // Get stored token
  getToken() {
    return localStorage.getItem(this.tokenKey);
  }

  // Set token
  setToken(token) {
    localStorage.setItem(this.tokenKey, token);
  }

  // Get stored user info
  getUser() {
    const userStr = localStorage.getItem(this.userKey);
    return userStr ? JSON.parse(userStr) : null;
  }

  // Set user info
  setUser(user) {
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  // Check if admin is authenticated
  isAuthenticated() {
    const token = this.getToken();
    const user = this.getUser();
    return !!(token && user);
  }

  // Get authorization headers
  getAuthHeaders() {
    const token = this.getToken();
    return token ? {
      'Authorization': `Token ${token}`,
      'Content-Type': 'application/json'
    } : {
      'Content-Type': 'application/json'
    };
  }

  // Update admin profile
  async updateProfile(profileData) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/profile/update/`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(profileData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Profile update failed');
      }

      // Update stored user info
      this.setUser(data.user);

      return data;
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  }

  // Change admin password
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/auth/change-password/`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Password change failed');
      }

      return data;
    } catch (error) {
      console.error('Password change error:', error);
      throw error;
    }
  }

  // Verify token validity
  async verifyToken() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/auth/verify/`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        this.logout();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Token verification error:', error);
      this.logout();
      return false;
    }
  }

  // Get admin dashboard data
  async getDashboardData() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/dashboard/`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Failed to fetch dashboard data');
      }

      return data;
    } catch (error) {
      console.error('Dashboard data error:', error);
      throw error;
    }
  }

  // Refresh user data
  async refreshUserData() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/profile/`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Failed to refresh user data');
      }

      this.setUser(data.user);
      return data.user;
    } catch (error) {
      console.error('Refresh user data error:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const adminAuthService = new AdminAuthService();
export default adminAuthService;
