import React, { useState } from 'react';
import { <PERSON>, <PERSON>po<PERSON>, Button, InputLabel, Di<PERSON><PERSON>, Stack } from '@mui/material';
import { Download as DownloadIcon, Upload as UploadIcon } from '@mui/icons-material';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// CSV Template definitions for each entity type
const csvTemplates = {
  'Customers': {
    headers: ['customer_code', 'customer_name', 'phone_number', 'email', 'channel', 'address', 'state_city', 'town'],
    sampleData: [
      ['CUST001', 'John Doe', '+234-************', '<EMAIL>', 'Retail', '123 Main Street', 'Lagos', 'Ikeja'],
      ['CUST002', 'Jane Smith', '+234-************', '<EMAIL>', 'Wholesale', '456 Market Road', 'Abuja', 'Garki']
    ]
  },
  'Brands': {
    headers: ['brand_name', 'brand_code', 'category', 'description', 'status'],
    sampleData: [
      ['Lipton', 'LIP001', 'Beverages', 'Tea and beverage products', 'Active'],
      ['Dove', 'DOV001', 'Personal Care', 'Beauty and personal care products', 'Active']
    ]
  },
  'SKUs': {
    headers: ['sku_code', 'sku_name', 'brand_code', 'category', 'unit_price', 'pack_size', 'status'],
    sampleData: [
      ['LIP001-250', 'Lipton Yellow Label Tea 250g', 'LIP001', 'Beverages', '850.00', '250g', 'Active'],
      ['DOV001-100', 'Dove Beauty Bar 100g', 'DOV001', 'Personal Care', '450.00', '100g', 'Active']
    ]
  },
  'Schemes': {
    headers: ['scheme_name', 'scheme_code', 'description', 'start_date', 'end_date', 'reward_type', 'reward_value', 'status'],
    sampleData: [
      ['Summer Promotion', 'SUM2024', 'Summer discount scheme', '2024-06-01', '2024-08-31', 'Percentage', '15', 'Active'],
      ['Loyalty Bonus', 'LOY2024', 'Loyalty points bonus', '2024-01-01', '2024-12-31', 'Points', '500', 'Active']
    ]
  },
  'Promotions': {
    headers: ['promotion_name', 'promotion_code', 'scheme_code', 'target_audience', 'start_date', 'end_date', 'budget', 'status'],
    sampleData: [
      ['Summer Sale Campaign', 'CAMP001', 'SUM2024', 'All Customers', '2024-06-01', '2024-08-31', '1000000', 'Active'],
      ['New Customer Welcome', 'CAMP002', 'LOY2024', 'New Customers', '2024-01-01', '2024-12-31', '500000', 'Active']
    ]
  }
};

export default function UploadCsvForm({ label, endpoint }) {
  const [file, setFile] = useState(null);
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
    setStatus('');
  };

  // Function to generate and download CSV template
  const downloadTemplate = () => {
    const template = csvTemplates[label];
    if (!template) {
      setStatus('Template not available for this upload type.');
      return;
    }

    // Create CSV content
    const csvContent = [
      template.headers.join(','),
      ...template.sampleData.map(row => row.join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${label.toLowerCase()}_template.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setStatus(`Template downloaded: ${label.toLowerCase()}_template.csv`);
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    if (!file) {
      setStatus('Please select a CSV file.');
      return;
    }
    setLoading(true);
    setStatus('');
    try {
      const formData = new FormData();
      formData.append('file', file);
      const token = localStorage.getItem('token');
      const res = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
        },
        body: formData,
      });
      if (!res.ok) {
        const err = await res.json();
        setStatus(err.detail || 'Upload failed.');
      } else {
        setStatus('Upload successful!');
        setFile(null);
      }
    } catch (error) {
      setStatus('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2, bgcolor: '#fafafa' }}>
      <Typography variant="subtitle1" fontWeight={600} mb={2} color="text.primary">
        {label} CSV Upload
      </Typography>

      {/* Template Download Section */}
      <Box sx={{ mb: 3, p: 2, bgcolor: '#f0f8ff', borderRadius: 1, border: '1px solid #e3f2fd' }}>
        <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
          Step 1: Download Template
        </Typography>
        <Typography variant="caption" color="text.secondary" mb={1} display="block">
          Download the CSV template to see the required format and fill in your data
        </Typography>
        {csvTemplates[label] && (
          <Typography variant="caption" color="text.secondary" mb={2} display="block" sx={{ fontStyle: 'italic' }}>
            Required columns: {csvTemplates[label].headers.join(', ')}
          </Typography>
        )}
        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={downloadTemplate}
          size="small"
          sx={{ mb: 1 }}
        >
          Download {label} Template
        </Button>
      </Box>

      {/* File Upload Section */}
      <Box component="form" onSubmit={handleUpload}>
        <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
          Step 2: Upload Your CSV File
        </Typography>
        <Typography variant="caption" color="text.secondary" mb={2} display="block">
          Select your completed CSV file to upload
        </Typography>

        <Box sx={{ mb: 2 }}>
          <InputLabel htmlFor={`csv-upload-${label}`} sx={{ mb: 1, fontSize: '0.875rem' }}>
            Select CSV File
          </InputLabel>
          <input
            id={`csv-upload-${label}`}
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            style={{
              marginBottom: 12,
              padding: '8px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              width: '100%',
              fontSize: '14px'
            }}
          />
        </Box>

        <Button
          type="submit"
          variant="contained"
          disabled={loading || !file}
          startIcon={<UploadIcon />}
          sx={{
            bgcolor: 'primary.main',
            '&:hover': { bgcolor: 'primary.dark' }
          }}
        >
          {loading ? 'Uploading...' : `Upload ${label}`}
        </Button>
      </Box>

      {/* Status Message */}
      {status && (
        <Box sx={{ mt: 2, p: 1.5, borderRadius: 1, bgcolor: status.includes('successful') || status.includes('downloaded') ? '#e8f5e8' : '#ffebee' }}>
          <Typography
            variant="body2"
            color={status.includes('successful') || status.includes('downloaded') ? 'success.main' : 'error.main'}
            sx={{ fontSize: '0.875rem' }}
          >
            {status}
          </Typography>
        </Box>
      )}
    </Box>
  );
}
