import React, { useState, useMemo } from 'react';
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent,
  Grid,
  IconButton,
  Avatar,
  TextField,
  useTheme,
  useMediaQuery,
  CssBaseline,
  ThemeProvider,
  createTheme,
  Divider,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Storefront as BrandsIcon,
  Person as ProfileIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  Category
} from '@mui/icons-material';

const drawerWidth = 280;
const collapsedDrawerWidth = 64;

const skuData = [
  { name: '<PERSON>NORR BEEF BTF 17X40X8G', week1: 6500, week2: 7500 },
  { name: '<PERSON><PERSON>OR<PERSON> CHICKEN BTF 17X40X8G', week1: 6500, week2: 7500 },
  { name: '<PERSON><PERSON><PERSON>R BEEF BTF 17X40X8G', week1: 6500, week2: 7500 },
  { name: 'KNORR BEEF BTF 17X40X8G', week1: 6500, week2: 7500 },
  { name: 'KNORR BEEF BTF 17X40X8G', week1: 6500, week2: 7500 },
  { name: 'KNORR BEEF BTF 17X40X8G', week1: 6500, week2: 7500 },
];

const tabs = [
  'Brand Performance (Weekly)',
  'SKU Performance',
  'Weekly Sales Trends'
];

export default function DashboardLayout() {
  // State management combining both versions
  const [activeTab, setActiveTab] = useState(0);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [selectedTab, setSelectedTab] = useState(1);
  const [weekMonth, setWeekMonth] = useState('Week');
  const [collapsed, setCollapsed] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  // React Router hooks from main branch
  const navigate = useNavigate();
  const location = useLocation();

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Professional color palette from fa branch
  const customTheme = useMemo(
    () =>
      createTheme({
        palette: {
          mode: darkMode ? 'dark' : 'light',
          primary: {
            main: darkMode ? '#2196f3' : '#1976d2'
          },
          background: {
            default: darkMode ? '#18191a' : '#f8f9fa',
            paper: darkMode ? '#232323' : '#fff'
          },
          text: {
            primary: darkMode ? '#fff' : '#222',
            secondary: darkMode ? '#b0b3b8' : '#555'
          }
        },
        typography: {
          fontFamily: 'Inter, Roboto, Arial, sans-serif',
          fontWeightBold: 700,
          fontWeightMedium: 600,
          fontWeightRegular: 400,
        }
      }),
    [darkMode]
  );

  // Navigation items combining both approaches
  const navigationItems = [
    { label: 'Dashboard', icon: <DashboardIcon />, index: 0, path: '/dashboard' },
    { label: 'Brands', icon: <Category />, index: 1, path: '/brands' },
    { label: 'Profile', icon: <ProfileIcon />, index: 2, path: '/profile' },
    { label: 'Settings', icon: <SettingsIcon />, index: 3, path: '/settings' },
  ];

  // Event handlers
  const handleDrawerToggle = () => setMobileOpen(!mobileOpen);
  const handleCollapseToggle = () => setCollapsed((prev) => !prev);
  const handleMenu = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  
  const handleNavigation = (path, index) => {
    setActiveTab(index);
    navigate(path);
    handleClose();
  };

  const handleLogout = () => {
    navigate("/login");
    handleClose();
  };

  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  const formatCurrency = (amount) => `₦${amount.toLocaleString()}`;

  const drawer = (
    <Box
      sx={{
        height: '100%',
        background: customTheme.palette.background.paper,
        display: 'flex',
        flexDirection: 'column',
        boxShadow: { xs: 'none', sm: 2 }
      }}
    >
      {/* Logo/Header Section */}
      <Box
        sx={{
          p: collapsed ? 1 : 3,
          textAlign: 'center',
          mb: 2,
          minHeight: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'space-between'
        }}
      >
        {!collapsed && (
          <Typography
            variant="h5"
            sx={{
              fontWeight: 700,
              color: customTheme.palette.text.primary,
              letterSpacing: '0.5px',
              fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' },
              cursor: 'pointer'
            }}
            onClick={() => navigate("/dashboard")}
          >
            Project Innov8
          </Typography>
        )}
        {!isMobile && (
          <IconButton onClick={handleCollapseToggle} size="small" sx={{ ml: collapsed ? 0 : 1 }}>
            {collapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
          </IconButton>
        )}
      </Box>
      {/* Navigation List */}
      <List sx={{ px: 1, flex: 1 }}>
        {navigationItems.map((item) => (
          <ListItem
            key={item.label}
            disablePadding
            sx={{
              justifyContent: collapsed ? 'center' : 'flex-start',
              width: '100%'
            }}
          >
            <ListItemButton
              onClick={() => handleNavigation(item.path, item.index)}
              sx={{
                margin: collapsed ? '8px 8px' : '8px 16px',
                borderRadius: '12px',
                py: 1.2,
                background: (activeTab === item.index || isActive(item.path)) 
                  ? customTheme.palette.primary.main 
                  : 'transparent',
                '&:hover': {
                  background: customTheme.palette.action.hover,
                },
                justifyContent: collapsed ? 'center' : 'flex-start',
                minHeight: 48,
                transition: 'background 0.2s',
                borderLeft: isActive(item.path) && !collapsed
                  ? `4px solid ${customTheme.palette.primary.main}`
                  : '4px solid transparent',
              }}
            >
              <ListItemIcon
                sx={{
                  color: (activeTab === item.index || isActive(item.path))
                    ? customTheme.palette.getContrastText(customTheme.palette.primary.main)
                    : customTheme.palette.text.primary,
                  minWidth: 0,
                  mr: collapsed ? 0 : 2,
                  justifyContent: 'center'
                }}
              >
                {item.icon}
              </ListItemIcon>
              {!collapsed && (
                <ListItemText
                  primary={item.label}
                  primaryTypographyProps={{
                    fontWeight: (activeTab === item.index || isActive(item.path)) ? 600 : 400,
                    color: (activeTab === item.index || isActive(item.path))
                      ? customTheme.palette.getContrastText(customTheme.palette.primary.main)
                      : customTheme.palette.text.primary,
                    fontSize: '1rem'
                  }}
                />
              )}
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      
      {/* Logout Section */}
      <Box sx={{ px: 1, pb: 2 }}>
        <Divider sx={{ mb: 2 }} />
        <ListItem disablePadding>
          <ListItemButton
            onClick={handleLogout}
            sx={{
              margin: collapsed ? '8px 8px' : '8px 16px',
              borderRadius: '12px',
              py: 1.2,
              '&:hover': {
                background: customTheme.palette.action.hover,
              },
              justifyContent: collapsed ? 'center' : 'flex-start',
              minHeight: 48,
            }}
          >
            <ListItemIcon
              sx={{
                color: customTheme.palette.text.primary,
                minWidth: 0,
                mr: collapsed ? 0 : 2,
                justifyContent: 'center'
              }}
            >
              <LogoutIcon />
            </ListItemIcon>
            {!collapsed && (
              <ListItemText
                primary="Logout"
                primaryTypographyProps={{
                  color: customTheme.palette.text.primary,
                  fontSize: '1rem'
                }}
              />
            )}
          </ListItemButton>
        </ListItem>
      </Box>
    </Box>
  );

  // Example values for performance (from fa branch)
  const targetPurchases = 200000;
  const actualPurchases = 150000;
  const incentiveEarned = 5000;
  const kpiPercentage = (actualPurchases / targetPurchases) * 100;

  return (
    <ThemeProvider theme={customTheme}>
      <CssBaseline />
      <Box sx={{ display: 'flex', background: customTheme.palette.background.default, minHeight: '100vh' }}>
        {/* App Bar */}
        <AppBar
          position="fixed"
          sx={{
            width: { sm: `calc(100% - ${(collapsed ? collapsedDrawerWidth : drawerWidth)}px)` },
            ml: { sm: `${collapsed ? collapsedDrawerWidth : drawerWidth}px` },
            background: customTheme.palette.background.paper,
            color: customTheme.palette.text.primary,
            boxShadow: { xs: 1, sm: 2 },
            borderBottom: `1px solid ${darkMode ? '#333' : '#e0e0e0'}`,
            elevation: 0,
            transition: 'width 0.2s, margin-left 0.2s',
            zIndex: (theme) => theme.zIndex.drawer + 1,
          }}
        >
          <Toolbar>
            {isMobile && (
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ mr: 2 }}
              >
                <MenuIcon sx={{ color: customTheme.palette.text.primary }} />
              </IconButton>
            )}
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                flexGrow: 1,
                color: customTheme.palette.text.primary,
                fontWeight: 700,
                fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' },
                cursor: 'pointer'
              }}
              onClick={() => navigate("/dashboard")}
            >
              Dashboard
            </Typography>
            {/* Theme Selector */}
            <IconButton
              sx={{
                background: customTheme.palette.background.paper,
                p: 1,
                mr: 2,
                border: `1px solid ${customTheme.palette.divider}`,
                '&:hover': {
                  background: customTheme.palette.action.hover
                }
              }}
              onClick={() => setDarkMode((prev) => !prev)}
              aria-label="toggle theme"
            >
              {darkMode ? (
                <Brightness7Icon sx={{ color: customTheme.palette.text.primary }} />
              ) : (
                <Brightness4Icon sx={{ color: customTheme.palette.text.primary }} />
              )}
            </IconButton>
            
            {/* User Menu */}
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenu}
              color="inherit"
            >
              <Avatar
                sx={{
                  background: customTheme.palette.primary.main,
                  color: customTheme.palette.getContrastText(customTheme.palette.primary.main),
                  width: 36,
                  height: 36,
                  fontWeight: 700,
                  fontSize: '1rem'
                }}
              >
                U
              </Avatar>
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "right",
              }}
              keepMounted
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              <MenuItem onClick={() => handleNavigation("/profile", 2)}>
                Profile
              </MenuItem>
              <MenuItem onClick={() => handleNavigation("/settings", 3)}>
                Settings
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>Logout</MenuItem>
            </Menu>
          </Toolbar>
        </AppBar>

        {/* Navigation Drawer */}
        <Box
          component="nav"
          sx={{
            width: { sm: collapsed ? collapsedDrawerWidth : drawerWidth },
            flexShrink: { sm: 0 },
            transition: 'width 0.2s'
          }}
        >
          {/* Mobile Drawer */}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{ keepMounted: true }}
            sx={{
              display: { xs: 'block', sm: 'none' },
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
            }}
          >
            {drawer}
          </Drawer>
          {/* Desktop Drawer */}
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', sm: 'block' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: collapsed ? collapsedDrawerWidth : drawerWidth,
                border: 'none',
                transition: 'width 0.2s'
              },
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: { xs: 1, sm: 3 },
            width: { sm: `calc(100% - ${(collapsed ? collapsedDrawerWidth : drawerWidth)}px)` },
            transition: 'width 0.2s'
          }}
        >
          <Toolbar />
          
          {/* Render either dashboard content or routed components */}
          {location.pathname === '/dashboard' ? (
            <>
              {/* Store Dashboard Header */}
              <Box sx={{ mb: 4 }}>
                <Typography
                  variant="h4"
                  sx={{
                    color: customTheme.palette.text.primary,
                    fontWeight: 700,
                    mb: 1,
                    fontSize: { xs: '1.3rem', sm: '2rem' }
                  }}
                >
                  Store Dashboard
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: customTheme.palette.text.secondary,
                    mb: 1,
                    fontSize: { xs: '0.95rem', sm: '1rem' }
                  }}
                >
                  Welcome back, Ngozi!
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: customTheme.palette.text.secondary,
                    fontSize: { xs: '0.85rem', sm: '1rem' }
                  }}
                >
                  Gold Tier
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: customTheme.palette.text.secondary,
                    fontSize: { xs: '0.85rem', sm: '1rem' }
                  }}
                >
                  Salesman: Chinedu Okoro (080-123-4567)
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: customTheme.palette.text.secondary,
                    fontSize: { xs: '0.85rem', sm: '1rem' }
                  }}
                >
                  Distributor: Lagos Distributors (080-987-6543)
                </Typography>
              </Box>

              {/* Date Range Filters */}
              <Grid container spacing={2} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" sx={{ mb: 1, color: customTheme.palette.text.primary }}>
                    From
                  </Typography>
                  <TextField
                    type="date"
                    value={fromDate}
                    onChange={e => setFromDate(e.target.value)}
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      background: customTheme.palette.background.paper,
                      borderRadius: '12px',
                      '& .MuiInputBase-root': { color: customTheme.palette.text.primary },
                      '& .MuiOutlinedInput-notchedOutline': { borderColor: customTheme.palette.divider }
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" sx={{ mb: 1, color: customTheme.palette.text.primary }}>
                    To
                  </Typography>
                  <TextField
                    type="date"
                    value={toDate}
                    onChange={e => setToDate(e.target.value)}
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      background: customTheme.palette.background.paper,
                      borderRadius: '12px',
                      '& .MuiInputBase-root': { color: customTheme.palette.text.primary },
                      '& .MuiOutlinedInput-notchedOutline': { borderColor: customTheme.palette.divider }
                    }}
                  />
                </Grid>
              </Grid>

              {/* Performance Section */}
              <Typography
                variant="h5"
                sx={{
                  mb: 3,
                  color: customTheme.palette.text.primary,
                  fontWeight: 700,
                  fontSize: { xs: '1.1rem', sm: '1.5rem' }
                }}
              >
                Performance
              </Typography>

              {/* Performance Metrics Cards */}
              <Grid container spacing={2} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={4}>
                  <Card
                    sx={{
                      background: customTheme.palette.background.paper,
                      borderRadius: '12px',
                      boxShadow: 1
                    }}
                  >
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: customTheme.palette.text.secondary,
                          mb: 1,
                          fontSize: { xs: '0.95rem', sm: '1rem' }
                        }}
                      >
                        Target Purchases
                      </Typography>
                      <Typography
                        variant="h4"
                        sx={{
                          color: customTheme.palette.text.primary,
                          fontWeight: 700,
                          fontSize: { xs: '1.2rem', sm: '2rem' }
                        }}
                      >
                        {formatCurrency(targetPurchases)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Card
                    sx={{
                      background: customTheme.palette.background.paper,
                      borderRadius: '12px',
                      boxShadow: 1
                    }}
                  >
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: customTheme.palette.text.secondary,
                          mb: 1,
                          fontSize: { xs: '0.95rem', sm: '1rem' }
                        }}
                      >
                        Actual Purchases
                      </Typography>
                      <Typography
                        variant="h4"
                        sx={{
                          color: customTheme.palette.text.primary,
                          fontWeight: 700,
                          fontSize: { xs: '1.2rem', sm: '2rem' }
                        }}
                      >
                        {formatCurrency(actualPurchases)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Card
                    sx={{
                      background: customTheme.palette.background.paper,
                      borderRadius: '12px',
                      boxShadow: 1
                    }}
                  >
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: customTheme.palette.text.secondary,
                          mb: 1,
                          fontSize: { xs: '0.95rem', sm: '1rem' }
                        }}
                      >
                        Incentive Earned
                      </Typography>
                      <Typography
                        variant="h4"
                        sx={{
                          color: customTheme.palette.text.primary,
                          fontWeight: 700,
                          fontSize: { xs: '1.2rem', sm: '2rem' }
                        }}
                      >
                        {formatCurrency(incentiveEarned)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* KPI Progress */}
              <Card
                sx={{
                  background: customTheme.palette.background.paper,
                  borderRadius: '12px',
                  boxShadow: 1,
                  mb: 4
                }}
              >
                <CardContent>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      justifyContent: 'space-between',
                      alignItems: { xs: 'flex-start', sm: 'center' },
                      mb: 2
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        color: customTheme.palette.text.primary,
                        fontWeight: 700,
                        fontSize: { xs: '1rem', sm: '1.25rem' }
                      }}
                    >
                      KPI versus Actual
                    </Typography>
                    <Typography
                      variant="h6"
                      sx={{
                        color: customTheme.palette.text.primary,
                        fontWeight: 700,
                        fontSize: { xs: '1rem', sm: '1.25rem' }
                      }}
                    >
                      {Math.round(kpiPercentage)}%
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      width: '100%',
                      height: 12,
                      borderRadius: 6,
                      background: customTheme.palette.background.default,
                      border: `1px solid ${customTheme.palette.divider}`,
                      position: 'relative',
                      overflow: 'hidden'
                    }}
                  >
                    <Box
                      sx={{
                        width: `${kpiPercentage}%`,
                        height: '100%',
                        background: customTheme.palette.primary.main,
                        borderRadius: 6
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>

              {/* Tabs Section */}
              <Box sx={{ mb: 3 }}>
                <Box
                  sx={{
                    display: 'flex',
                    gap: 1,
                    borderRadius: '16px',
                    p: 1,
                    background: customTheme.palette.background.paper,
                    border: `1px solid ${customTheme.palette.divider}`,
                    flexDirection: { xs: 'column', sm: 'row' }
                  }}
                >
                  {tabs.map((tab, index) => (
                    <Box
                      key={tab}
                      onClick={() => setSelectedTab(index)}
                      sx={{
                        px: 3,
                        py: 1.5,
                        borderRadius: '12px',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        background: selectedTab === index ? customTheme.palette.primary.main : 'transparent',
                        color: selectedTab === index
                          ? customTheme.palette.getContrastText(customTheme.palette.primary.main)
                          : customTheme.palette.text.primary,
                        fontWeight: selectedTab === index ? 700 : 400,
                        mb: { xs: 1, sm: 0 }
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{ fontSize: { xs: '0.95rem', sm: '1rem' } }}
                      >
                        {tab}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Box>

              {/* Week/Month Toggle */}
              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  mb: 3,
                  flexDirection: { xs: 'column', sm: 'row' }
                }}
              >
                <Box
                  onClick={() => setWeekMonth('Week')}
                  sx={{
                    px: 4,
                    py: 1,
                    borderRadius: '16px',
                    cursor: 'pointer',
                    background: weekMonth === 'Week' ? customTheme.palette.primary.main : customTheme.palette.background.paper,
                    color: weekMonth === 'Week'
                      ? customTheme.palette.getContrastText(customTheme.palette.primary.main)
                      : customTheme.palette.text.primary,
                    border: `1px solid ${customTheme.palette.divider}`,
                    fontWeight: weekMonth === 'Week' ? 700 : 400,
                    transition: 'all 0.2s',
                    mb: { xs: 1, sm: 0 }
                  }}
                >
                  Week
                </Box>
                <Box
                  onClick={() => setWeekMonth('Month')}
                  sx={{
                    px: 4,
                    py: 1,
                    borderRadius: '16px',
                    cursor: 'pointer',
                    background: weekMonth === 'Month' ? customTheme.palette.primary.main : customTheme.palette.background.paper,
                    color: weekMonth === 'Month'
                      ? customTheme.palette.getContrastText(customTheme.palette.primary.main)
                      : customTheme.palette.text.primary,
                    border: `1px solid ${customTheme.palette.divider}`,
                    fontWeight: weekMonth === 'Month' ? 700 : 400,
                    transition: 'all 0.2s'
                  }}
                >
                  Month
                </Box>
              </Box>

              {/* Product Performance Table */}
              <Card
                sx={{
                  background: customTheme.palette.background.paper,
                  borderRadius: '12px',
                  boxShadow: 1
                }}
              >
                <CardContent>
                  {/* Table Header */}
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      justifyContent: 'space-between',
                      mb: 3,
                      pb: 2,
                      borderBottom: `1px solid ${customTheme.palette.divider}`
                    }}
                  >
                    <Box sx={{ display: 'flex', gap: 4 }}>
                      <Typography
                        variant="subtitle2"
                        sx={{ color: customTheme.palette.text.primary, fontWeight: 700 }}
                      >
                        SKUs
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 4 }}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: customTheme.palette.text.primary,
                          fontWeight: 700,
                          cursor: 'pointer',
                          pb: 1,
                          borderBottom: weekMonth === 'Week' ? `2px solid ${customTheme.palette.primary.main}` : 'none'
                        }}
                        onClick={() => setWeekMonth('Week')}
                      >
                        Week
                      </Typography>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: customTheme.palette.text.primary,
                          cursor: 'pointer',
                          borderBottom: weekMonth === 'Month' ? `2px solid ${customTheme.