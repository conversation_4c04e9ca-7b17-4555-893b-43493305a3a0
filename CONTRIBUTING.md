# Contributing to Project Innov8

## Project Architecture and File Structure

Project Innov8 follows a standard React application structure with Material UI for the UI components and React Router for navigation.

## Step-by-Step Guide for Developers

### 1. Clone the Repository

```bash
git clone https://github.com/your-organization/project-innov8-frontend.git
cd project-innov8-frontend
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Create a New Branch

Always create a new branch for your work using the following naming convention:
`initialname/feature-description`

For example:
```bash
git checkout -b ea/add-brand-filter
```

Where "ea" are your initials and "add-brand-filter" is a brief description of the feature you're working on.

### 4. Run the Development Server

```bash
npm run dev
```

This will start the development server at http://localhost:5173.

### 5. Make Your Changes

Follow these guidelines when making changes:
- Keep components small and focused on a single responsibility
- Use Material UI components for consistent styling
- Follow the existing code style and patterns
- Add comments for complex logic

### 6. Test Your Changes

Make sure to test your changes thoroughly:
```bash
npm run test
```

### 7. Commit Your Changes

Use clear and descriptive commit messages:
```bash
git add .
git commit -m "Add brand filtering functionality"
```

### 8. Push Your Changes

**IMPORTANT: Always push to your feature branch, NEVER to the main branch.**

```bash
git push origin ea/add-brand-filter
```

### 9. Create a Pull Request

Go to the repository on GitHub and create a pull request from your branch to the main branch. Your code will be reviewed by the project maintainers before being merged.

## Branch Protection

The main branch is protected and requires pull request reviews before merging. Direct pushes to the main branch are not allowed.

## Creating a Bug Report

When reporting bugs, please include:

1. A clear and descriptive title
2. Steps to reproduce the issue
3. Expected behavior
4. Actual behavior
5. Screenshots if applicable
6. Browser and operating system information

Use the following template for bug reports:

```
Title: [Bug] Brief description of the issue

Description:
A clear description of what the bug is.

Steps to Reproduce:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

Expected Behavior:
A clear description of what you expected to happen.

Actual Behavior:
A clear description of what actually happened.

Screenshots:
If applicable, add screenshots to help explain your problem.

Environment:
 - OS: [e.g. Windows 10, macOS]
 - Browser: [e.g. Chrome, Firefox]
 - Version: [e.g. 22]
```

## Feature Request Template

Use the following template for feature requests:

```
Title: [Feature] Brief description of the feature

Description:
A clear description of what you want to happen.

Why:
Explain why this feature would be useful.

Possible Implementation:
Optional: suggest an idea for implementing the feature.

Additional Context:
Add any other context or screenshots about the feature request here.
```

---

Documentation by Emmanuel Adubi.