import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  TextField,
  Button,
  ButtonGroup,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Divider,
  ThemeProvider,
  createTheme,
  alpha,
  Menu,
  MenuItem,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import { LocalOffer as OfferIcon, Close as CloseIcon } from '@mui/icons-material';

// Data
const performanceData = {
  targetPurchases: 200000,
  actualPurchases: 150000,
  incentiveEarned: 5000,
  kpiPercentage: 75
};

const purchaseTrendsData = [
  { week: 'Week 1', purchases: 140000 },
  { week: 'Week 2', purchases: 180000 },
  { week: 'Week 3', purchases: 160000 },
  { week: 'Week 4', purchases: 190000 },
  { week: 'Week 5', purchases: 220000 }
];

const brandPerformanceData = [
  { brand: 'CLOSE-UP', week1: 6500, week2: 7500, month: 14000, logo: '/api/placeholder/40/40' },
  { brand: 'PEPSODENT', week1: 8500, week2: 9200, month: 17700, logo: '/api/placeholder/40/40' },
  { brand: 'KNORR', week1: 12000, week2: 13500, month: 25500, logo: '/api/placeholder/40/40' },
  { brand: 'ROYCO', week1: 8800, week2: 9200, month: 18000, logo: '/api/placeholder/40/40' },
  { brand: 'Blue G', week1: 7300, week2: 8200, month: 15500, logo: '/api/placeholder/40/40' },
  { brand: 'Blue F', week1: 6800, week2: 7200, month: 14000, logo: '/api/placeholder/40/40' },
  { brand: 'Blue D', week1: 5900, week2: 6200, month: 12100, logo: '/api/placeholder/40/40' }
];

const skuPerformanceData = [
  { name: 'KNORR BEEF BTF 17X40X8G', week1: 6500, week2: 7500, month: 14000 },
  { name: 'KNORR CHICKEN BTF 17X40X8G', week1: 6500, week2: 7500, month: 14000 },
  { name: 'KNORR BEEF BTF 17X40X8G', week1: 6500, week2: 7500, month: 14000 },
  { name: 'KNORR BEEF BTF 17X40X8G', week1: 6500, week2: 7500, month: 14000 },
  { name: 'KNORR BEEF BTF 17X40X8G', week1: 6500, week2: 7500, month: 14000 }
];

const storeInfo = {
  agent: 'Welcome back, Ngozi!',
  tier: 'Tier: Gold',
  salesRep: 'SalesRep: Olumide Olowu (080-124-4567)',
  distributor: 'Distributor: Ayo Distributors (080-987-6543)',
  location: 'Ibadan, Nigeria'
};

// Theme for light and dark modes
const lightTheme = createTheme({
  palette: {
    mode: 'light',
    background: {
      default: '#fff',
      paper: '#fff'
    },
    text: {
      primary: '#000',
      secondary: '#222'
    },
    primary: {
      main: '#000'
    },
    success: {
      main: '#008000'
    },
    warning: {
      main: '#FFA500'
    },
    error: {
      main: '#d32f2f'
    }
  }
});

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    background: {
      default: '#181818',
      paper: '#232323'
    },
    text: {
      primary: '#fff',
      secondary: '#ccc'
    },
    primary: {
      main: '#fff'
    },
    success: {
      main: '#00e676'
    },
    warning: {
      main: '#ffb300'
    },
    error: {
      main: '#ef5350'
    }
  }
});

// Child components
function BrandPerformanceTable({ data, viewMode, formatCurrency }) {
  return (
    <TableContainer component={Paper} variant="outlined">
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Brand</TableCell>
            <TableCell align="right">Week 1</TableCell>
            <TableCell align="right">Week 2</TableCell>
            {viewMode === 'month' && <TableCell align="right">Month Total</TableCell>}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((brand) => (
            <TableRow key={brand.brand} hover>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar src={brand.logo} alt={brand.brand} sx={{ width: 32, height: 32, mr: 2 }} />
                  <Typography variant="body2" fontWeight={600}>
                    {brand.brand}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell align="right">{formatCurrency(brand.week1)}</TableCell>
              <TableCell align="right">{formatCurrency(brand.week2)}</TableCell>
              {viewMode === 'month' && (
                <TableCell align="right">
                  <Typography variant="body2" fontWeight={600}>
                    {formatCurrency(brand.month)}
                  </Typography>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

function SkuPerformanceTable({ data, formatCurrency }) {
  return (
    <TableContainer component={Paper} variant="outlined">
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>SKU</TableCell>
            <TableCell align="right">Week 1</TableCell>
            <TableCell align="right">Week 2</TableCell>
            <TableCell align="right">Change</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((sku, idx) => {
            const change = ((sku.week2 - sku.week1) / sku.week1) * 100;
            return (
              <TableRow key={idx} hover>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {sku.name}
                  </Typography>
                </TableCell>
                <TableCell align="right">{formatCurrency(sku.week1)}</TableCell>
                <TableCell align="right">{formatCurrency(sku.week2)}</TableCell>
                <TableCell align="right">
                  <Chip
                    label={`${change > 0 ? '+' : ''}${change.toFixed(1)}%`}
                    color={change > 0 ? 'success' : change < 0 ? 'error' : 'default'}
                    size="small"
                  />
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

const PromotionBanner = ({ onDismiss, theme }) => {
  return (
    <Card sx={{
      mb: 4,
      backgroundColor: theme.palette.mode === 'dark' ? '#333' : '#fff',
      color: theme.palette.mode === 'dark' ? '#fff' : '#000',
      position: 'relative',
      overflow: 'hidden',
      borderRadius: '12px',
      border: '1px solid',
      borderColor: theme.palette.mode === 'dark' ? '#555' : '#e0e0e0',
      boxShadow: '0 2px 12px rgba(0,0,0,0.08)'
    }}>
      <Box sx={{ p: 3 }}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 2,
          flexWrap: 'wrap'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <OfferIcon sx={{ 
              fontSize: 40,
              color: theme.palette.mode === 'dark' ? '#fff' : '#000' 
            }} />
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                NEW PROMOTION
              </Typography>
              <Typography variant="body1">
                Exclusive 15% bonus on all orders above ₦200,000 this month
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button 
              variant="contained" 
              sx={{ 
                backgroundColor: theme.palette.mode === 'dark' ? '#fff' : '#000',
                color: theme.palette.mode === 'dark' ? '#000' : '#fff',
                fontWeight: 600,
                minWidth: 120,
                '&:hover': {
                  backgroundColor: theme.palette.mode === 'dark' ? '#eee' : '#333'
                }
              }}
            >
              Details
            </Button>
            <IconButton 
              onClick={onDismiss} 
              sx={{ 
                color: theme.palette.mode === 'dark' ? '#fff' : '#000',
                border: '1px solid',
                borderColor: theme.palette.mode === 'dark' ? '#555' : '#e0e0e0'
              }}
              aria-label="Dismiss promotion"
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </Box>
    </Card>
  );
};

export default function Customer() {
  const [tab, setTab] = useState(0);
  const [viewMode, setViewMode] = useState('week');
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [themeMode, setThemeMode] = useState('light');
  const [anchorEl, setAnchorEl] = useState(null);
  const [showBanner, setShowBanner] = useState(true);

  const theme = themeMode === 'light' ? lightTheme : darkTheme;

  const formatCurrency = (amount) => `₦${amount.toLocaleString()}`;
  const getPerformanceColor = (percentage) => {
    if (percentage >= 80) return theme.palette.success.main;
    if (percentage >= 60) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  // Profile menu handlers
  const handleProfileMenuOpen = (event) => setAnchorEl(event.currentTarget);
  const handleProfileMenuClose = () => setAnchorEl(null);

  // Theme toggle handler
  const handleThemeToggle = () => setThemeMode((prev) => (prev === 'light' ? 'dark' : 'light'));

  // Tab handler
  const handleTabChange = (event, newValue) => setTab(newValue);

  return (
    <ThemeProvider theme={theme}>
      <Box
        sx={{
          width: '100vw',
          minHeight: '100vh',
          bgcolor: 'background.default',
          color: 'text.primary',
          p: { xs: 2, md: 4 },
          boxSizing: 'border-box',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Top Bar */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
            px: { xs: 1, md: 2 },
            maxWidth: 1600,
            mx: 'auto',
            width: '100%',
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 700 }}>
            Unilever Nigeria
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Tooltip title={themeMode === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}>
              <IconButton onClick={handleThemeToggle} color="inherit">
                {themeMode === 'light' ? <Brightness4Icon /> : <Brightness7Icon />}
              </IconButton>
            </Tooltip>
            <Tooltip title="Profile">
              <IconButton onClick={handleProfileMenuOpen} color="inherit">
                <AccountCircleIcon />
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleProfileMenuClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <MenuItem disabled>
                <Avatar sx={{ mr: 1 }} /> {storeInfo.agent.replace('Welcome back, ', '')}
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleProfileMenuClose}>Profile</MenuItem>
              <MenuItem onClick={handleProfileMenuClose}>Logout</MenuItem>
            </Menu>
          </Box>
        </Box>

        {/* Promotion Banner */}
        {showBanner && (
          <PromotionBanner onDismiss={() => setShowBanner(false)} theme={theme} />
        )}

        {/* Main Content */}
        <Box
          sx={{
            flex: 1,
            width: '100%',
            maxWidth: 1600,
            mx: 'auto',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'stretch',
          }}
        >
          <Grid container spacing={4} sx={{ flex: 1, height: '100%' }}>
            {/* Left: Store Info & Performance */}
            <Grid item xs={12} md={6} lg={5} sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              <Box sx={{ pr: { md: 4, lg: 8 }, flex: 1 }}>
                <Typography variant="h5" sx={{ fontWeight: 700, mb: 2 }}>
                  Store Dashboard
                </Typography>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" color="text.secondary">{storeInfo.agent}</Typography>
                  <Typography variant="body2" color="text.secondary">{storeInfo.outlet}</Typography>
                  <Typography variant="body2" color="text.secondary">{storeInfo.salesRep}</Typography>
                  <Typography variant="body2" color="text.secondary">{storeInfo.distributor}</Typography>
                </Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                  Performance
                </Typography>
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} sm={4}>
                    <Card variant="outlined" sx={{ minHeight: 90 }}>
                      <CardContent>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Target Purchases
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                          {formatCurrency(performanceData.targetPurchases)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Card variant="outlined" sx={{ minHeight: 90 }}>
                      <CardContent>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Actual Purchases
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                          {formatCurrency(performanceData.actualPurchases)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Card variant="outlined" sx={{ minHeight: 90 }}>
                      <CardContent>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Incentive Earned
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                          {formatCurrency(performanceData.incentiveEarned)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                    KPI versus Actual
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{ flex: 1 }}>
                      <LinearProgress
                        variant="determinate"
                        value={performanceData.kpiPercentage}
                        sx={{
                          height: 10,
                          borderRadius: 4,
                          backgroundColor: alpha(theme.palette.mode === 'dark' ? '#fff' : '#000', 0.1), // Faint bg
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: theme.palette.mode === 'dark' ? '#fff' : '#000' // White in dark, black in light
                          }
                        }}
                      />
                    </Box>
                    <Typography variant="body2" sx={{ minWidth: 32 }}>
                      {performanceData.kpiPercentage}%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>

            {/* Right: Filters, Tabs, Tab Content */}
            <Grid item xs={12} md={6} lg={7} sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              <Box sx={{ pl: { md: 4, lg: 8 }, width: '100%', flex: 1, display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 3, mb: 3 }}>
                  <TextField
                    size="small"
                    type="date"
                    label="From"
                    value={fromDate}
                    onChange={(e) => setFromDate(e.target.value)}
                    sx={{ minWidth: 160 }}
                    InputLabelProps={{ shrink: true }}
                  />
                  <TextField
                    size="small"
                    type="date"
                    label="To"
                    value={toDate}
                    onChange={(e) => setToDate(e.target.value)}
                    sx={{ minWidth: 160 }}
                    InputLabelProps={{ shrink: true }}
                  />
                </Box>
                <Tabs
                  value={tab}
                  onChange={handleTabChange}
                  aria-label="Performance Tabs"
                  sx={{
                    borderBottom: 1,
                    borderColor: 'divider',
                    mb: 3,
                    '.MuiTab-root': { textTransform: 'none', fontWeight: 600 }
                  }}
                >
                  <Tab label="Brand Performance (Weekly)" />
                  <Tab label="SKU Performance" />
                  <Tab label="Weekly Sales Trends" />
                </Tabs>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 3,
                    minHeight: 400,
                    width: '100%',
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  {/* Brand Performance Tab */}
                  {tab === 0 && (
                    <>
                      <ButtonGroup size="small" variant="outlined" sx={{ mb: 3 }}>
                        <Button
                          variant={viewMode === 'week' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('week')}
                        >
                          Week
                        </Button>
                        <Button
                          variant={viewMode === 'month' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('month')}
                        >
                          Month
                        </Button>
                        <Button
                          variant={viewMode === 'ytd' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('ytd')}
                        >
                          Year to Date
                        </Button>
                      </ButtonGroup>
                      <Box sx={{ flex: 1, minHeight: 0 }}>
                        <BrandPerformanceTable
                          data={brandPerformanceData}
                          viewMode={viewMode}
                          formatCurrency={formatCurrency}
                        />
                      </Box>
                    </>
                  )}
                  {/* SKU Performance Tab */}
                  {tab === 1 && (
                    <>
                      <ButtonGroup size="small" variant="outlined" sx={{ mb: 3 }}>
                        <Button
                          variant={viewMode === 'week' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('week')}
                        >
                          Week
                        </Button>
                        <Button
                          variant={viewMode === 'month' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('month')}
                        >
                          Month
                        </Button>
                        <Button
                          variant={viewMode === 'ytd' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('ytd')}
                        >
                          Year to Date
                        </Button>
                      </ButtonGroup>
                      <Box sx={{ flex: 1, minHeight: 0 }}>
                        <SkuPerformanceTable
                          data={skuPerformanceData}
                          formatCurrency={formatCurrency}
                        />
                      </Box>
                    </>
                  )}
                  {/* Weekly Sales Trends Tab */}
                  {tab === 2 && (
                    <>
                      <ButtonGroup size="small" variant="outlined" sx={{ mb: 3 }}>
                        <Button
                          variant={viewMode === 'week' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('week')}
                        >
                          Week
                        </Button>
                        <Button
                          variant={viewMode === 'month' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('month')}
                        >
                          Month
                        </Button>
                        <Button
                          variant={viewMode === 'ytd' ? 'contained' : 'outlined'}
                          onClick={() => setViewMode('ytd')}
                        >
                          Year to Date
                        </Button>
                      </ButtonGroup>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          Purchase Trends
                        </Typography>
                        <Typography variant="h5" sx={{ fontWeight: 700 }}>
                          {formatCurrency(performanceData.actualPurchases)}
                        </Typography>
                        <Typography variant="body2" color="success.main">
                          Last 13 Weeks +5%
                        </Typography>
                      </Box>
                      <Box sx={{ flex: 1, minHeight: 0 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={purchaseTrendsData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="week" />
                            <YAxis />
                            <RechartsTooltip formatter={(value) => formatCurrency(value)} />
                            <Line
                              type="monotone"
                              dataKey="purchases"
                              stroke={theme.palette.primary.main}
                              strokeWidth={3}
                              dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 6 }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </Box>
                    </>
                  )}
                </Paper>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </ThemeProvider>
  );
}