import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Alert,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import apiService from '../../services/apiService';

export default function CustomerManagement() {
  const [customers, setCustomers] = useState([]);
  const [distributors, setDistributors] = useState([]);
  const [dsrs, setDSRs] = useState([]);
  const [filteredDSRs, setFilteredDSRs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [message, setMessage] = useState({ type: '', text: '' });

  const [formData, setFormData] = useState({
    customer_code: '',
    username: '',
    email: '',
    phone_number: '',
    channel: 'OMLS',
    customer_type: 'bronze',
    physical_address: '',
    state_city: '',
    town: '',
    distributor: '',
    dsr: ''
  });

  const customerTypes = ['bronze', 'silver', 'gold', 'platinum'];
  const channels = ['OMLS', 'Traditional Trade', 'Modern Trade'];

  useEffect(() => {
    loadCustomers();
    loadDistributors();
    loadDSRs();
  }, []);

  useEffect(() => {
    // Filter DSRs based on selected distributor
    if (formData.distributor) {
      const filtered = dsrs.filter(dsr => dsr.distributor === parseInt(formData.distributor));
      setFilteredDSRs(filtered);
    } else {
      setFilteredDSRs([]);
    }
  }, [formData.distributor, dsrs]);

  const loadCustomers = async () => {
    try {
      setLoading(true);
      const response = await apiService.getCustomers();
      setCustomers(response.results || response);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to load customers' });
    } finally {
      setLoading(false);
    }
  };

  const loadDistributors = async () => {
    try {
      const response = await apiService.getDistributors();
      setDistributors(response.results || response);
    } catch (error) {
      console.error('Failed to load distributors:', error);
    }
  };

  const loadDSRs = async () => {
    try {
      const response = await apiService.getDSRs();
      setDSRs(response.results || response);
    } catch (error) {
      console.error('Failed to load DSRs:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
      // Clear DSR when distributor changes
      ...(name === 'distributor' && { dsr: '' })
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      if (editingCustomer) {
        await apiService.updateCustomer(editingCustomer.id, formData);
        setMessage({ type: 'success', text: 'Customer updated successfully!' });
      } else {
        await apiService.createCustomer(formData);
        setMessage({ type: 'success', text: 'Customer created successfully!' });
      }
      
      setDialogOpen(false);
      resetForm();
      loadCustomers();
    } catch (error) {
      setMessage({ type: 'error', text: error.message || 'Operation failed' });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (customer) => {
    setEditingCustomer(customer);
    setFormData({
      customer_code: customer.customer_code || '',
      username: customer.username || '',
      email: customer.email || '',
      phone_number: customer.phone_number || '',
      channel: customer.channel || 'OMLS',
      customer_type: customer.customer_type || 'bronze',
      physical_address: customer.physical_address || '',
      state_city: customer.state_city || '',
      town: customer.town || '',
      distributor: customer.distributor || '',
      dsr: customer.dsr || ''
    });
    setDialogOpen(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this customer?')) {
      try {
        setLoading(true);
        await apiService.deleteCustomer(id);
        setMessage({ type: 'success', text: 'Customer deleted successfully!' });
        loadCustomers();
      } catch (error) {
        setMessage({ type: 'error', text: error.message || 'Delete failed' });
      } finally {
        setLoading(false);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      customer_code: '',
      username: '',
      email: '',
      phone_number: '',
      channel: 'OMLS',
      customer_type: 'bronze',
      physical_address: '',
      state_city: '',
      town: '',
      distributor: '',
      dsr: ''
    });
    setEditingCustomer(null);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    resetForm();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" fontWeight={600} mb={1}>
            Customer Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage customers with distributor and DSR assignments
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setDialogOpen(true)}
        >
          Add Customer
        </Button>
      </Box>

      {message.text && (
        <Alert 
          severity={message.type} 
          sx={{ mb: 3 }}
          onClose={() => setMessage({ type: '', text: '' })}
        >
          {message.text}
        </Alert>
      )}

      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>Customer</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Contact</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Type & Channel</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Distributor</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>DSR</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {customers.map((customer) => (
                  <TableRow key={customer.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PersonIcon sx={{ mr: 2, color: 'primary.main' }} />
                        <Box>
                          <Typography variant="body2" fontWeight={600}>
                            {customer.username}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {customer.customer_code}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">{customer.phone_number}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {customer.email}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Chip 
                          label={customer.customer_type} 
                          size="small" 
                          color={
                            customer.customer_type === 'platinum' ? 'primary' :
                            customer.customer_type === 'gold' ? 'warning' :
                            customer.customer_type === 'silver' ? 'info' : 'default'
                          }
                        />
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                          {customer.channel}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {customer.distributor_name || 'Not Assigned'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {customer.dsr_name || 'Not Assigned'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(customer)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(customer.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingCustomer ? 'Edit Customer' : 'Add New Customer'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="customer_code"
                label="Customer Code"
                fullWidth
                required
                value={formData.customer_code}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="username"
                label="Username"
                fullWidth
                required
                value={formData.username}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="email"
                label="Email"
                type="email"
                fullWidth
                required
                value={formData.email}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="phone_number"
                label="Phone Number"
                fullWidth
                value={formData.phone_number}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Customer Type</InputLabel>
                <Select
                  name="customer_type"
                  value={formData.customer_type}
                  onChange={handleInputChange}
                  label="Customer Type"
                >
                  {customerTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Channel</InputLabel>
                <Select
                  name="channel"
                  value={formData.channel}
                  onChange={handleInputChange}
                  label="Channel"
                >
                  {channels.map((channel) => (
                    <MenuItem key={channel} value={channel}>
                      {channel}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="physical_address"
                label="Physical Address"
                fullWidth
                multiline
                rows={2}
                value={formData.physical_address}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="state_city"
                label="State/City"
                fullWidth
                value={formData.state_city}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="town"
                label="Town"
                fullWidth
                value={formData.town}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Distributor</InputLabel>
                <Select
                  name="distributor"
                  value={formData.distributor}
                  onChange={handleInputChange}
                  label="Distributor"
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {distributors.map((distributor) => (
                    <MenuItem key={distributor.id} value={distributor.id}>
                      {distributor.distributor_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={!formData.distributor}>
                <InputLabel>DSR</InputLabel>
                <Select
                  name="dsr"
                  value={formData.dsr}
                  onChange={handleInputChange}
                  label="DSR"
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {filteredDSRs.map((dsr) => (
                    <MenuItem key={dsr.id} value={dsr.id}>
                      {dsr.name} ({dsr.employee_code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Saving...' : (editingCustomer ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
