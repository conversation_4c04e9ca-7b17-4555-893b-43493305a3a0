import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Login from './pages/auth/Login';
import AdminLayout from './layouts/admin';
import DashboardLayout from './layouts/dashboard';
import CustomerLayout from './layouts/customer';
import RSMDashboard from './layouts/rsmdashboard';
import ASMDashboard from './layouts/asmdashboard';

const theme = createTheme({
  palette: {
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
  },
});

export default function App() {
  return (
    <ThemeProvider theme={theme}>
      <Box>
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Navigate to="/login" replace />} /> {/* Redirect root to /login */}
            <Route path="/login" element={<Login />} />
            <Route path="/admin/*" element={<AdminLayout />} />
            <Route path="/dashboard/*" element={<DashboardLayout />} />
            <Route path="/customer/*" element={<CustomerLayout />} />
            <Route path="/rsm" element={<RSMDashboard />} />
            <Route path="/asm" element={<ASMDashboard />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </BrowserRouter>
      </Box>
    </ThemeProvider>
  );
}









