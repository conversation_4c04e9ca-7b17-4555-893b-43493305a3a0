import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Avatar
} from '@mui/material';
import VpnKeyIcon from '@mui/icons-material/VpnKey';

export default function VerifyOtp() {
  const [otp, setOtp] = useState('');
  const navigate = useNavigate();
  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);
  const phoneNumber = queryParams.get('phone');

  useEffect(() => {
    if (!phoneNumber) {
      navigate('/login');
    }
  }, [phoneNumber, navigate]);

  const handleVerify = async (e) => {
    e.preventDefault();
    if (!otp || !phoneNumber) {
      alert('Please enter the OTP.');
      return;
    }
    try {
      // Send OTP and phone number to backend for verification
      const res = await fetch('http://127.0.0.1:8000/api/users/auth/verify-otp/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone: phoneNumber, otp })
      });
      if (!res.ok) {
        const err = await res.json();
        alert(err.detail || 'OTP verification failed');
        return;
      }
      const data = await res.json();
      // Store token and redirect
      localStorage.setItem('token', data.token);
      navigate('/customer/dashboard');
    } catch (error) {
      alert('Network error. Please try again.');
    }
  };

  return (
    <Box
      sx={{
        width: '100vw',
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: '#f5f5f5',
      }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          width: '100%',
          maxWidth: 400,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Avatar sx={{ bgcolor: 'primary.main', mb: 2 }}>
          <VpnKeyIcon />
        </Avatar>
        <Typography variant="h6" mb={2}>
          Enter OTP sent to {phoneNumber}
        </Typography>
        <Box component="form" onSubmit={handleVerify} sx={{ width: '100%' }}>
          <TextField
            label="OTP"
            value={otp}
            onChange={e => setOtp(e.target.value)}
            fullWidth
            margin="normal"
            inputProps={{ maxLength: 6 }}
          />
          <Button type="submit" variant="contained" color="primary" fullWidth sx={{ mt: 2 }}>
            Verify OTP
          </Button>
        </Box>
      </Paper>
    </Box>
  );
}
