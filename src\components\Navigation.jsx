import React from 'react';
import { 
  Box, AppBar, Toolbar, Typography, Button, 
  IconButton, Menu, MenuItem, Avatar
} from '@mui/material';
import { AccountCircle } from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
// If you were importing from routes.ts, use the direct paths instead

export default function Navigation() {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const navigate = useNavigate();
  const location = useLocation();
  
  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  
  const handleNavigation = (path) => {
    navigate(path);
    handleClose();
  };
  
  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };
  
  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static">
        <Toolbar>
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ 
              flexGrow: 1, 
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
            onClick={() => navigate('/')}
          >
            Project Innov8
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Button 
              color="inherit" 
              onClick={() => navigate('/dashboard')}
              sx={{ 
                mx: 1,
                fontWeight: isActive('/dashboard') ? 'bold' : 'normal',
                borderBottom: isActive('/dashboard') ? '2px solid white' : 'none'
              }}
            >
              Dashboard
            </Button>
            
            <Button 
              color="inherit" 
              onClick={() => navigate('/brands')}
              sx={{ 
                mx: 1,
                fontWeight: isActive('/brands') ? 'bold' : 'normal',
                borderBottom: isActive('/brands') ? '2px solid white' : 'none'
              }}
            >
              Brands
            </Button>
            
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenu}
              color="inherit"
              sx={{ ml: 2 }}
            >
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>U</Avatar>
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              <MenuItem onClick={() => handleNavigation('/profile')}>Profile</MenuItem>
              <MenuItem onClick={() => handleNavigation('/settings')}>Settings</MenuItem>
              <MenuItem onClick={() => handleNavigation('/login')}>Logout</MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>
    </Box>
  );
}
