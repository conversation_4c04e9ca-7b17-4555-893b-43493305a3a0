import React, { useState } from 'react';
import { Box, Typography, Button, InputLabel } from '@mui/material';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export default function UploadCsvForm({ label, endpoint }) {
  const [file, setFile] = useState(null);
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
    setStatus('');
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    if (!file) {
      setStatus('Please select a CSV file.');
      return;
    }
    setLoading(true);
    setStatus('');
    try {
      const formData = new FormData();
      formData.append('file', file);
      const token = localStorage.getItem('token');
      const res = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
        },
        body: formData,
      });
      if (!res.ok) {
        const err = await res.json();
        setStatus(err.detail || 'Upload failed.');
      } else {
        setStatus('Upload successful!');
        setFile(null);
      }
    } catch (error) {
      setStatus('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleUpload} sx={{ p: 2, border: '1px solid #eee', borderRadius: 2, bgcolor: '#fafafa' }}>
      <Typography variant="subtitle1" fontWeight={600} mb={1}>{label} CSV Upload</Typography>
      <InputLabel htmlFor={`csv-upload-${label}`}>Select CSV File</InputLabel>
      <input
        id={`csv-upload-${label}`}
        type="file"
        accept=".csv"
        onChange={handleFileChange}
        style={{ marginBottom: 12 }}
      />
      <Button type="submit" variant="contained" disabled={loading || !file}>
        {loading ? 'Uploading...' : 'Upload'}
      </Button>
      {status && (
        <Typography variant="body2" color={status.includes('successful') ? 'success.main' : 'error.main'} mt={2}>
          {status}
        </Typography>
      )}
    </Box>
  );
}
