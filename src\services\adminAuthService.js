const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

class AdminAuthService {
  constructor() {
    this.tokenKey = 'adminToken';
    this.userKey = 'adminUser';
  }

  // Get CSRF token from cookies
  getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  // Login admin user - try multiple possible endpoints
  async login(username, password) {
    // List of possible authentication endpoints to try
    const endpoints = [
      '/api/users/auth/login/',
      '/api/auth/login/',
      '/api/admin/login/',
      '/admin/api/login/',
      '/api/token/',
      '/api/login/',
      '/auth/login/',
      '/api/users/login/',
      '/users/auth/login/',
      '/api/admin/auth/login/'
    ];

    let lastError = null;

    for (const endpoint of endpoints) {
      try {
        console.log(`Trying authentication endpoint: ${API_BASE_URL}${endpoint}`);

        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username, password })
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Login successful with endpoint: ${endpoint}`);

          // Store token and user info
          this.setToken(data.token || data.access_token || data.access);
          this.setUser(data.user || data);

          return data;
        } else if (response.status !== 404) {
          // If it's not a 404, this might be the right endpoint but wrong credentials
          let errorMessage = 'Invalid credentials';
          try {
            const errorData = await response.json();
            errorMessage = errorData.detail || errorData.error || errorMessage;
          } catch (e) {
            // If response is not JSON, use default message
          }
          throw new Error(errorMessage);
        }
        // If 404, continue to next endpoint
      } catch (error) {
        if (error.message !== 'Invalid credentials' && !error.message.includes('fetch')) {
          console.log(`❌ Endpoint ${endpoint} failed:`, error.message);
          lastError = error;
          continue;
        } else {
          // This is a credentials error, not an endpoint error
          throw error;
        }
      }
    }

    // If we get here, no endpoint worked
    throw new Error('No valid authentication endpoint found. Please check your backend configuration.');
  }

  // Logout admin user
  logout() {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
    // Redirect to admin login
    window.location.href = '/admin/login';
  }

  // Get stored token
  getToken() {
    return localStorage.getItem(this.tokenKey);
  }

  // Set token
  setToken(token) {
    localStorage.setItem(this.tokenKey, token);
  }

  // Get stored user info
  getUser() {
    const userStr = localStorage.getItem(this.userKey);
    return userStr ? JSON.parse(userStr) : null;
  }

  // Set user info
  setUser(user) {
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  // Check if admin is authenticated
  isAuthenticated() {
    const token = this.getToken();
    const user = this.getUser();
    return !!(token && user);
  }

  // Get authorization headers
  getAuthHeaders() {
    const token = this.getToken();
    return token ? {
      'Authorization': `Token ${token}`,
      'Content-Type': 'application/json'
    } : {
      'Content-Type': 'application/json'
    };
  }

  // Update admin profile
  async updateProfile(profileData) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/profile/update/`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(profileData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Profile update failed');
      }

      // Update stored user info
      this.setUser(data.user);

      return data;
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  }

  // Change admin password
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/auth/change-password/`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Password change failed');
      }

      return data;
    } catch (error) {
      console.error('Password change error:', error);
      throw error;
    }
  }

  // Verify token validity
  async verifyToken() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/users/me/`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
        credentials: 'include'
      });

      if (!response.ok) {
        this.logout();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Token verification error:', error);
      this.logout();
      return false;
    }
  }

  // Get admin dashboard data
  async getDashboardData() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/dashboard/`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Failed to fetch dashboard data');
      }

      return data;
    } catch (error) {
      console.error('Dashboard data error:', error);
      throw error;
    }
  }

  // Refresh user data
  async refreshUserData() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/profile/`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || data.message || 'Failed to refresh user data');
      }

      this.setUser(data.user);
      return data.user;
    } catch (error) {
      console.error('Refresh user data error:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const adminAuthService = new AdminAuthService();
export default adminAuthService;
