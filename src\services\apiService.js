const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Get authorization headers
  getAuthHeaders() {
    const token = localStorage.getItem('adminToken') || localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Token ${token}` })
    };
  }

  // Generic API methods
  async get(endpoint) {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`GET ${endpoint} error:`, error);
      throw error;
    }
  }

  async post(endpoint, data) {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || errorData.message || `HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`POST ${endpoint} error:`, error);
      throw error;
    }
  }

  async put(endpoint, data) {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || errorData.message || `HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`PUT ${endpoint} error:`, error);
      throw error;
    }
  }

  async delete(endpoint) {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.status === 204 ? null : await response.json();
    } catch (error) {
      console.error(`DELETE ${endpoint} error:`, error);
      throw error;
    }
  }

  // Customer API methods
  async getCustomers(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/api/users/customers/${queryString ? `?${queryString}` : ''}`;
    return this.get(endpoint);
  }

  async getCustomer(id) {
    return this.get(`/api/users/customers/${id}/`);
  }

  async createCustomer(customerData) {
    return this.post('/api/users/customers/', customerData);
  }

  async updateCustomer(id, customerData) {
    return this.put(`/api/users/customers/${id}/`, customerData);
  }

  async deleteCustomer(id) {
    return this.delete(`/api/users/customers/${id}/`);
  }

  // Distributor API methods
  async getDistributors(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/api/users/distributors/${queryString ? `?${queryString}` : ''}`;
    return this.get(endpoint);
  }

  async getDistributor(id) {
    return this.get(`/api/users/distributors/${id}/`);
  }

  async createDistributor(distributorData) {
    return this.post('/api/users/distributors/', distributorData);
  }

  async updateDistributor(id, distributorData) {
    return this.put(`/api/users/distributors/${id}/`, distributorData);
  }

  async deleteDistributor(id) {
    return this.delete(`/api/users/distributors/${id}/`);
  }

  // DSR API methods
  async getDSRs(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/api/users/dsrs/${queryString ? `?${queryString}` : ''}`;
    return this.get(endpoint);
  }

  async getDSR(id) {
    return this.get(`/api/users/dsrs/${id}/`);
  }

  async createDSR(dsrData) {
    return this.post('/api/users/dsrs/', dsrData);
  }

  async updateDSR(id, dsrData) {
    return this.put(`/api/users/dsrs/${id}/`, dsrData);
  }

  async deleteDSR(id) {
    return this.delete(`/api/users/dsrs/${id}/`);
  }

  // Territory API methods (assuming territories exist)
  async getTerritories() {
    return this.get('/api/territories/');
  }

  async getTerritory(id) {
    return this.get(`/api/territories/${id}/`);
  }

  // Utility methods for relationships
  async getDSRsByDistributor(distributorId) {
    return this.getDSRs({ distributor: distributorId });
  }

  async getDistributorsByTerritory(territoryId) {
    return this.getDistributors({ territory: territoryId });
  }

  async getCustomersByDistributor(distributorId) {
    return this.getCustomers({ distributor: distributorId });
  }

  async getCustomersByDSR(dsrId) {
    return this.getCustomers({ dsr: dsrId });
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;
