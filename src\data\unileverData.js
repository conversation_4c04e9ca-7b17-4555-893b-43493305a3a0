export const unileverBrands = [
  {
    id: '1',
    name: '<PERSON>',
    category: 'Personal Care',
    description: 'Beauty and personal care products',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Dove_logo.svg/1200px-Dove_logo.svg.png'
  },
  {
    id: '2',
    name: 'Axe',
    category: 'Personal Care',
    description: 'Men\'s grooming products',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/Axe_logo.svg/1200px-Axe_logo.svg.png'
  },
  {
    id: '3',
    name: 'Lipton',
    category: 'Food & Beverage',
    description: 'Tea products',
    logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/3/3b/Lipton_Logo.svg/1200px-Lipton_Logo.svg.png'
  },
  {
    id: '4',
    name: 'Knorr',
    category: 'Food & Beverage',
    description: 'Food products and seasonings',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/97/Knorr_logo.svg/1200px-Knorr_logo.svg.png'
  },
  {
    id: '5',
    name: 'Hellmann\'s',
    category: 'Food & Beverage',
    description: 'Condiments and sauces',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e9/Hellmann%27s_logo.svg/1200px-Hellmann%27s_logo.svg.png'
  }
];

export const brandSkus = {
  '1': [
    { id: '101', name: 'Dove Beauty Bar', description: 'Moisturizing soap', price: 3.99 },
    { id: '102', name: 'Dove Shampoo', description: 'Daily care shampoo', price: 5.99 },
    { id: '103', name: 'Dove Deodorant', description: '48-hour protection', price: 4.49 }
  ],
  '2': [
    { id: '201', name: 'Axe Body Spray', description: 'Men\'s fragrance', price: 6.99 },
    { id: '202', name: 'Axe Shower Gel', description: 'Refreshing body wash', price: 4.99 }
  ],
  '3': [
    { id: '301', name: 'Lipton Yellow Label Tea', description: 'Black tea bags', price: 3.49 },
    { id: '302', name: 'Lipton Green Tea', description: 'Green tea bags', price: 3.99 },
    { id: '303', name: 'Lipton Ice Tea', description: 'Ready-to-drink tea', price: 1.99 }
  ],
  '4': [
    { id: '401', name: 'Knorr Chicken Stock Cubes', description: 'Flavor enhancer', price: 2.49 },
    { id: '402', name: 'Knorr Soup Mix', description: 'Quick soup preparation', price: 1.99 }
  ],
  '5': [
    { id: '501', name: 'Hellmann\'s Mayonnaise', description: 'Classic mayo', price: 4.29 },
    { id: '502', name: 'Hellmann\'s Ketchup', description: 'Tomato ketchup', price: 3.49 }
  ]
};

