import React, { useState, useMemo } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  A<PERSON>Bar,
  <PERSON>l<PERSON>,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  CssBaseline,
  useTheme,
  useMediaQuery,
  Grid,
  Card,
  CardContent,
  ThemeProvider,
  createTheme,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Chip
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  Campaign as CampaignIcon,
  Redeem as RedeemIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  Send as SendIcon,
  Assessment as AssessmentIcon
} from "@mui/icons-material";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Tooltip,
  Legend
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Too<PERSON><PERSON>,
  Legend
);

const drawerWidth = 240;

const navItems = [
  { label: "Dashboard", icon: <DashboardIcon /> },
  { label: "Campaigns", icon: <CampaignIcon /> },
  { label: "Rewards", icon: <RedeemIcon /> },
  { label: "Members", icon: <PeopleIcon /> },
  { label: "Settings", icon: <SettingsIcon /> }
];

// Area Performance data
const areaPerformanceData = {
  labels: ["Area 1", "Area 2", "Area 3", "Area 4", "Area 5", "Area 6"],
  datasets: [
    {
      label: "Performance Score",
      backgroundColor: "#26a69a",
      borderRadius: 6,
      data: [85, 92, 78, 88, 95, 82]
    }
  ]
};

// Area Managers data
const areaManagers = [
  { 
    name: "Ethan Carter", 
    storesManaged: 25, 
    campaignsActive: 8, 
    rewardsSummary: "5000 points", 
    actions: "View Dashboard" 
  },
  { 
    name: "Olivia Bennett", 
    storesManaged: 30, 
    campaignsActive: 5, 
    rewardsSummary: "4500 points", 
    actions: "View Dashboard" 
  },
  { 
    name: "Noah Thompson", 
    storesManaged: 28, 
    campaignsActive: 6, 
    rewardsSummary: "4800 points", 
    actions: "View Dashboard" 
  },
  { 
    name: "Ava Martinez", 
    storesManaged: 33, 
    campaignsActive: 9, 
    rewardsSummary: "5200 points", 
    actions: "View Dashboard" 
  },
  { 
    name: "Liam Harris", 
    storesManaged: 22, 
    campaignsActive: 4, 
    rewardsSummary: "3800 points", 
    actions: "View Dashboard" 
  }
];

export default function RSMDashboard() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [active, setActive] = useState(0);
  const [areaManagerFilter, setAreaManagerFilter] = useState("All");
  const [campaignFilter, setCampaignFilter] = useState("All");

  // Black & White only theme
  const customTheme = useMemo(
    () =>
      createTheme({
        palette: {
          mode: "light",
          primary: {
            main: "#000",
            light: "#222",
            dark: "#000"
          },
          secondary: {
            main: "#fff",
            light: "#fff",
            dark: "#eee"
          },
          background: {
            default: "#fff",
            paper: "#fff"
          },
          text: {
            primary: "#000",
            secondary: "#222"
          },
          grey: {
            100: "#f5f5f5",
            200: "#e0e0e0",
            300: "#bdbdbd"
          }
        },
        typography: {
          fontFamily: "Inter, Roboto, Arial, sans-serif",
          fontWeightBold: 700,
          fontWeightMedium: 600,
          fontWeightRegular: 400,
          h6: {
            fontWeight: 600
          }
        },
        components: {
          MuiCard: {
            styleOverrides: {
              root: {
                borderRadius: 12,
                boxShadow: "0 2px 12px rgba(0,0,0,0.08)"
              }
            }
          }
        }
      }),
    []
  );

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const drawer = (
    <Box sx={{ height: "100%", background: customTheme.palette.background.paper }}>
      <Box sx={{ p: 3, textAlign: "left", borderBottom: `1px solid ${customTheme.palette.grey[200]}` }}>
        <Typography variant="h6" fontWeight={700} color="text.primary">
          Retail Rewards
        </Typography>
      </Box>
      <List sx={{ pt: 2 }}>
        {navItems.map((item, idx) => (
          <ListItem key={item.label} disablePadding sx={{ px: 1 }}>
            <ListItemButton
              selected={active === idx}
              onClick={() => setActive(idx)}
              sx={{
                borderRadius: 2,
                mx: 1,
                mb: 0.5,
                color: customTheme.palette.text.primary,
                "&.Mui-selected": {
                  background: customTheme.palette.primary.main,
                  color: "white",
                  "&:hover": {
                    background: customTheme.palette.primary.dark
                  }
                },
                "&:hover": {
                  background: customTheme.palette.grey[100]
                }
              }}
            >
              <ListItemIcon sx={{ 
                color: "inherit",
                minWidth: 40
              }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText 
                primary={item.label}
                primaryTypographyProps={{
                  fontSize: "0.9rem",
                  fontWeight: 500
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <ThemeProvider theme={customTheme}>
      <CssBaseline />
      <Box sx={{ display: "flex", minHeight: "100vh", background: customTheme.palette.background.default }}>
        <AppBar
          position="fixed"
          sx={{
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            ml: { sm: `${drawerWidth}px` },
            background: customTheme.palette.background.paper,
            color: customTheme.palette.text.primary,
            boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
            borderBottom: `1px solid ${customTheme.palette.grey[200]}`
          }}
        >
          <Toolbar>
            {isMobile && (
              <IconButton
                color="inherit"
                edge="start"
                onClick={() => setMobileOpen(!mobileOpen)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 600 }}>
              Dashboard
            </Typography>
            <IconButton sx={{ mr: 1, color: customTheme.palette.text.secondary }}>
              <NotificationsIcon />
            </IconButton>
            <Avatar sx={{
              bgcolor: customTheme.palette.primary.main,
              width: 36,
              height: 36
            }}>R</Avatar>
          </Toolbar>
        </AppBar>

        <Box
          component="nav"
          sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        >
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={() => setMobileOpen(false)}
            ModalProps={{ keepMounted: true }}
            sx={{
              display: { xs: "block", sm: "none" },
              "& .MuiDrawer-paper": {
                boxSizing: "border-box",
                width: drawerWidth,
                background: customTheme.palette.background.paper
              }
            }}
          >
            {drawer}
          </Drawer>
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: "none", sm: "block" },
              "& .MuiDrawer-paper": {
                boxSizing: "border-box",
                width: drawerWidth,
                background: customTheme.palette.background.paper,
                borderRight: `1px solid ${customTheme.palette.grey[200]}`
              }
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: { xs: 2, sm: 3 },
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            minHeight: "100vh"
          }}
        >
          <Toolbar />
          
          {/* Dashboard Header */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" fontWeight={700} color="text.primary" sx={{ mb: 1 }}>
              Regional Sales Manager Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Track performance across your area managers and stores
            </Typography>
          </Box>

          {/* Key Metrics Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={6} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: "center", py: 3 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Number of Area Managers
                  </Typography>
                  <Typography variant="h3" fontWeight={700} color="text.primary">
                    12
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: "center", py: 3 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Total Stores
                  </Typography>
                  <Typography variant="h3" fontWeight={700} color="text.primary">
                    250
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: "center", py: 3 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Points Issued
                  </Typography>
                  <Typography variant="h3" fontWeight={700} color="text.primary">
                    1,500,000
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: "center", py: 3 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Redemption Rate
                  </Typography>
                  <Typography variant="h3" fontWeight={700} color="text.primary">
                    75%
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Area Performance Comparison */}
          <Card sx={{ mb: 4 }}>
            <CardContent>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Area Performance Comparison
              </Typography>
              <Box sx={{ height: 300 }}>
                <Bar
                  data={areaPerformanceData}
                  options={{
                    plugins: { 
                      legend: { display: false },
                      tooltip: {
                        backgroundColor: customTheme.palette.background.paper,
                        titleColor: customTheme.palette.text.primary,
                        bodyColor: customTheme.palette.text.secondary,
                        borderColor: customTheme.palette.grey[300],
                        borderWidth: 1
                      }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      x: { 
                        ticks: { color: customTheme.palette.text.secondary },
                        grid: { display: false }
                      },
                      y: { 
                        ticks: { color: customTheme.palette.text.secondary },
                        grid: { color: customTheme.palette.grey[200] },
                        beginAtZero: true,
                        max: 100
                      }
                    }
                  }}
                />
              </Box>
            </CardContent>
          </Card>

          {/* Area Managers Section */}
          <Card>
            <CardContent>
              <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
                <Typography variant="h6" fontWeight={600}>
                  Area Managers
                </Typography>
                <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                  <FormControl size="small" sx={{ minWidth: 140 }}>
                    <InputLabel>Area Manager</InputLabel>
                    <Select
                      value={areaManagerFilter}
                      label="Area Manager"
                      onChange={(e) => setAreaManagerFilter(e.target.value)}
                    >
                      <MenuItem value="All">All</MenuItem>
                      <MenuItem value="Top Performers">Top Performers</MenuItem>
                      <MenuItem value="New Managers">New Managers</MenuItem>
                    </Select>
                  </FormControl>
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <InputLabel>Campaign</InputLabel>
                    <Select
                      value={campaignFilter}
                      label="Campaign"
                      onChange={(e) => setCampaignFilter(e.target.value)}
                    >
                      <MenuItem value="All">All</MenuItem>
                      <MenuItem value="Active">Active</MenuItem>
                      <MenuItem value="Completed">Completed</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        Name
                      </TableCell>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        Stores Managed
                      </TableCell>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        Campaigns Active
                      </TableCell>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        Rewards Summary
                      </TableCell>
                      <TableCell sx={{ fontWeight: 600, color: customTheme.palette.text.secondary }}>
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {areaManagers.map((manager, index) => (
                      <TableRow key={index} hover>
                        <TableCell sx={{ fontWeight: 500 }}>{manager.name}</TableCell>
                        <TableCell>{manager.storesManaged}</TableCell>
                        <TableCell>
                          <Chip 
                            label={manager.campaignsActive} 
                            size="small" 
                            color="primary" 
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>{manager.rewardsSummary}</TableCell>
                        <TableCell>
                          <Button 
                            size="small" 
                            variant="outlined" 
                            color="primary"
                            sx={{ textTransform: "none" }}
                          >
                            {manager.actions}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Action Buttons */}
              <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 3 }}>
                <Button 
                  variant="outlined" 
                  startIcon={<SendIcon />}
                  sx={{ textTransform: "none" }}
                >
                  Send Notification
                </Button>
                <Button 
                  variant="contained" 
                  startIcon={<AssessmentIcon />}
                  sx={{ textTransform: "none" }}
                >
                  Export Report
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </ThemeProvider>
  );
}