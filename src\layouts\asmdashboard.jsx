import React, { useState, useMemo } from "react";
import {
  Box,
  Typo<PERSON>,
  <PERSON>ppBar,
  Toolbar,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  CssBaseline,
  useMediaQuery,
  Grid,
  Card,
  CardContent,
  ThemeProvider,
  createTheme,
  Button
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  Campaign as CampaignIcon,
  Redeem as RedeemIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
} from "@mui/icons-material";

const drawerWidth = 240;

const navItems = [
  { label: "Dashboard", icon: <DashboardIcon /> },
  { label: "Campaigns", icon: <CampaignIcon /> },
  { label: "Rewards", icon: <RedeemIcon /> },
  { label: "Users", icon: <PeopleIcon /> },
  { label: "Settings", icon: <SettingsIcon /> },
];

export default function ASMDashboard() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useMediaQuery("(max-width:600px)");

  // Black & White only theme
  const customTheme = useMemo(
    () =>
      createTheme({
        palette: {
          mode: "light",
          primary: { main: "#000", light: "#222", dark: "#000" },
          secondary: { main: "#fff", light: "#fff", dark: "#eee" },
          background: { default: "#fff", paper: "#fff" },
          text: { primary: "#000", secondary: "#222" },
          grey: { 100: "#f5f5f5", 200: "#e0e0e0", 300: "#bdbdbd" }
        },
        typography: {
          fontFamily: "Inter, Roboto, Arial, sans-serif",
          fontWeightBold: 700,
          fontWeightMedium: 600,
          fontWeightRegular: 400,
          h6: { fontWeight: 600 }
        },
        components: {
          MuiCard: {
            styleOverrides: {
              root: {
                borderRadius: 12,
                boxShadow: "0 2px 12px rgba(0,0,0,0.08)"
              }
            }
          }
        }
      }),
    []
  );

  const drawer = (
    <Box>
      <Toolbar />
      <List>
        {navItems.map((item, idx) => (
          <ListItem key={item.label} disablePadding>
            <ListItemButton>
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <ThemeProvider theme={customTheme}>
      <CssBaseline />
      <Box sx={{ display: "flex", minHeight: "100vh", background: customTheme.palette.background.default }}>
        <AppBar
          position="fixed"
          sx={{
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            ml: { sm: `${drawerWidth}px` },
            background: customTheme.palette.background.paper,
            color: customTheme.palette.text.primary,
            boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
            borderBottom: `1px solid ${customTheme.palette.grey[200]}`
          }}
        >
          <Toolbar>
            {isMobile && (
              <IconButton
                color="inherit"
                edge="start"
                onClick={() => setMobileOpen(!mobileOpen)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 600 }}>
              ASM Dashboard
            </Typography>
            <IconButton sx={{ mr: 2, color: customTheme.palette.text.secondary }}>
              <NotificationsIcon />
            </IconButton>
            <Avatar sx={{
              bgcolor: customTheme.palette.primary.main,
              width: 36,
              height: 36,
              color: customTheme.palette.getContrastText(customTheme.palette.primary.main)
            }}>A</Avatar>
          </Toolbar>
        </AppBar>

        <Box
          component="nav"
          sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        >
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={() => setMobileOpen(false)}
            ModalProps={{ keepMounted: true }}
            sx={{
              display: { xs: "block", sm: "none" },
              "& .MuiDrawer-paper": {
                boxSizing: "border-box",
                width: drawerWidth,
                background: customTheme.palette.background.paper
              }
            }}
          >
            {drawer}
          </Drawer>
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: "none", sm: "block" },
              "& .MuiDrawer-paper": {
                boxSizing: "border-box",
                width: drawerWidth,
                background: customTheme.palette.background.paper,
                borderRight: `1px solid ${customTheme.palette.grey[200]}`
              }
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: { xs: 2, sm: 3 },
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            minHeight: "100vh"
          }}
        >
          <Toolbar />
          <Box sx={{ mb: 3 }}>
            <Typography variant="h5" fontWeight={600} color="text.primary" sx={{ mb: 1 }}>
              Dashboard
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Overview of ASM activities and performance
            </Typography>
          </Box>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent sx={{ textAlign: "center", py: 2.5 }}>
                  <Typography variant="h4" fontWeight={700} color="text.primary">
                    20
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Area Managers
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent sx={{ textAlign: "center", py: 2.5 }}>
                  <Typography variant="h4" fontWeight={700} color="text.primary">
                    120
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Campaigns
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent sx={{ textAlign: "center", py: 2.5 }}>
                  <Typography variant="h4" fontWeight={700} color="text.primary">
                    5000
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Rewards
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent sx={{ textAlign: "center", py: 2.5 }}>
                  <Typography variant="h4" fontWeight={700} color="text.primary">
                    92%
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Engagement
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </ThemeProvider>
  );
}